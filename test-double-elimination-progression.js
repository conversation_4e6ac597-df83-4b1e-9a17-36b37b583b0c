// Test Double Elimination Match Progression Logic
// This test simulates actual match completions to verify bracket progression

const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== DOUBLE ELIMINATION PROGRESSION TEST ===\n');

// Test participants - using 15 players to test bye logic (like in user's example)
const participants = [
    { id: '******************************************', name: 'Player Alpha', walletAddress: '******************************************', seed: 1 },
    { id: '******************************************', name: 'Player <PERSON>', walletAddress: '******************************************', seed: 2 },
    { id: '******************************************', name: 'Player <PERSON>', walletAddress: '******************************************', seed: 3 },
    { id: '0x456789013def123456789013def12345678abcd', name: 'Player Delta', walletAddress: '0x456789013def123456789013def12345678abcd', seed: 4 },
    { id: '******************************************', name: 'Player Echo', walletAddress: '******************************************', seed: 5 },
    { id: '******************************************', name: 'Player Foxtrot', walletAddress: '******************************************', seed: 6 },
    { id: '******************************************', name: 'Player Golf', walletAddress: '******************************************', seed: 7 },
    { id: '******************************************', name: 'Player Hotel', walletAddress: '******************************************', seed: 8 },
    { id: '******************************************', name: 'Player India', walletAddress: '******************************************', seed: 9 },
    { id: '******************************************', name: 'Player Juliet', walletAddress: '******************************************', seed: 10 },
    { id: '******************************************', name: 'Player Kilo', walletAddress: '******************************************', seed: 11 },
    { id: '******************************************', name: 'Player Lima', walletAddress: '******************************************', seed: 12 },
    { id: '******************************************', name: 'Player Mike', walletAddress: '******************************************', seed: 13 },
    { id: '******************************************', name: 'Player November', walletAddress: '******************************************', seed: 14 },
    { id: '******************************************', name: 'Player Oscar', walletAddress: '******************************************', seed: 15 }
];

// Generate initial bracket
console.log('Generating 15-player double elimination bracket...');
const bracket = generateBracket(participants, 'double-elimination', { matchFormat: 'bo3' });

console.log(`✅ Bracket generated with ${Object.keys(bracket.matchesById).length} matches`);
console.log(`Upper bracket rounds: ${bracket.metadata.numUpperRounds}`);
console.log(`Lower bracket rounds: ${bracket.metadata.numLowerRounds}`);

// Display initial bracket structure
console.log('\n--- INITIAL BRACKET STRUCTURE ---');
displayBracketStructure(bracket);

// Simulate match progression logic (mimicking the Firebase function logic)
function simulateMatchCompletion(bracket, matchId, winnerId, loserId) {
    console.log(`\n--- SIMULATING MATCH COMPLETION ---`);
    console.log(`Match: ${matchId}`);
    console.log(`Winner: ${winnerId}`);
    console.log(`Loser: ${loserId}`);
    
    const { matchesById } = bracket;
    const completedMatch = matchesById[matchId];
    
    if (!completedMatch) {
        console.error(`❌ Match ${matchId} not found!`);
        return false;
    }
    
    // Update match status
    completedMatch.status = 'COMPLETED';
    completedMatch.winnerId = winnerId;
    completedMatch.loserId = loserId;
    
    // Apply progression logic from functions/index.js
    if (completedMatch.isUpperBracket) {
        console.log('Processing upper bracket match...');
        
        // Handle winner progression using nextMatchId
        const winnerNextMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;
        
        if (winnerNextMatch) {
            console.log(`Moving winner to match: ${winnerNextMatch.id}`);
            // Check if participant1 is null or a TBD placeholder from this match
            if (winnerNextMatch.participant1Id === null || winnerNextMatch.participant1Id === `TBD_${completedMatch.id}`) {
                winnerNextMatch.participant1Id = winnerId;
                console.log(`  ✅ Set participant1 to ${winnerId}`);
            }
            // Check if participant2 is null or a TBD placeholder from this match
            else if (winnerNextMatch.participant2Id === null || winnerNextMatch.participant2Id === `TBD_${completedMatch.id}`) {
                winnerNextMatch.participant2Id = winnerId;
                console.log(`  ✅ Set participant2 to ${winnerId}`);
            }

            // Update status if both participants are now set
            if (winnerNextMatch.participant1Id && winnerNextMatch.participant2Id &&
                !winnerNextMatch.participant1Id.startsWith('TBD_') &&
                !winnerNextMatch.participant2Id.startsWith('TBD_')) {
                winnerNextMatch.status = 'PENDING';
                console.log(`✅ Winner match ${winnerNextMatch.id} is now PENDING`);
            }
        }
        
        // Handle loser progression using nextLoserMatchId
        const loserNextMatch = completedMatch.nextLoserMatchId ? matchesById[completedMatch.nextLoserMatchId] : null;
        
        if (loserNextMatch && loserId) {
            console.log(`Moving loser to lower bracket match: ${loserNextMatch.id}`);
            console.log(`  Current participants: ${loserNextMatch.participant1Id} vs ${loserNextMatch.participant2Id}`);
            console.log(`  Looking for TBD placeholder: TBD_${completedMatch.id}`);

            if (loserNextMatch.participant1Id === null || loserNextMatch.participant1Id === `TBD_${completedMatch.id}`) {
                loserNextMatch.participant1Id = loserId;
                console.log(`  ✅ Set participant1 to ${loserId}`);
            } else if (loserNextMatch.participant2Id === null || loserNextMatch.participant2Id === `TBD_${completedMatch.id}`) {
                loserNextMatch.participant2Id = loserId;
                console.log(`  ✅ Set participant2 to ${loserId}`);
            } else {
                console.log(`  ❌ Could not place loser - no matching slot found`);
            }
            
            // Update status if both participants are now set
            if (loserNextMatch.participant1Id && loserNextMatch.participant2Id &&
                !loserNextMatch.participant1Id.startsWith('TBD_') &&
                !loserNextMatch.participant2Id.startsWith('TBD_')) {
                loserNextMatch.status = 'PENDING';
                console.log(`✅ Loser match ${loserNextMatch.id} is now PENDING`);
            }
        } else {
            console.log('⚠️  No loser progression found for upper bracket match');
        }
    } else {
        console.log('Processing lower bracket match...');
        
        // Handle lower bracket progression
        const nextLowerMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;
        
        if (nextLowerMatch) {
            console.log(`Moving winner to next lower bracket match: ${nextLowerMatch.id}`);
            // Check if participant1 is null or a TBD placeholder from this match
            if (nextLowerMatch.participant1Id === null || nextLowerMatch.participant1Id === `TBD_${completedMatch.id}`) {
                nextLowerMatch.participant1Id = winnerId;
                console.log(`  ✅ Set participant1 to ${winnerId}`);
            }
            // Check if participant2 is null or a TBD placeholder from this match
            else if (nextLowerMatch.participant2Id === null || nextLowerMatch.participant2Id === `TBD_${completedMatch.id}`) {
                nextLowerMatch.participant2Id = winnerId;
                console.log(`  ✅ Set participant2 to ${winnerId}`);
            }

            // Update status if both participants are now set
            if (nextLowerMatch.participant1Id && nextLowerMatch.participant2Id &&
                !nextLowerMatch.participant1Id.startsWith('TBD_') &&
                !nextLowerMatch.participant2Id.startsWith('TBD_')) {
                nextLowerMatch.status = 'PENDING';
                console.log(`✅ Next lower bracket match ${nextLowerMatch.id} is now PENDING`);
            }
        }
    }
    
    return true;
}

function displayBracketStructure(bracket) {
    const { rounds, matchesById } = bracket;
    
    rounds.forEach((round, roundIndex) => {
        console.log(`\n${round.name}:`);
        round.matches.forEach(match => {
            const participant1 = bracket.participants.find(p => p.id === match.participant1Id);
            const participant2 = bracket.participants.find(p => p.id === match.participant2Id);
            
            const p1Name = participant1 ? participant1.name : (match.participant1Id || 'TBD');
            const p2Name = participant2 ? participant2.name : (match.participant2Id || 'TBD');
            
            const bracketType = match.isUpperBracket ? 'UB' : 'LB';
            const nextMatch = match.nextMatchId ? ` -> ${match.nextMatchId}` : '';
            const nextLoserMatch = match.nextLoserMatchId ? ` (L-> ${match.nextLoserMatchId})` : '';
            
            console.log(`  [${bracketType}] ${match.id}: ${p1Name} vs ${p2Name} [${match.status}]${nextMatch}${nextLoserMatch}`);
        });
    });
}

// Test progression by simulating matches
console.log('\n=== TESTING MATCH PROGRESSION ===');

// Find first upper bracket matches
const firstUpperRound = bracket.rounds.find(round => 
    round.matches.some(match => match.isUpperBracket)
);

if (firstUpperRound && firstUpperRound.matches.length >= 2) {
    const match1 = firstUpperRound.matches[0];
    const match2 = firstUpperRound.matches[1];
    
    console.log('\n--- STEP 1: Complete first upper bracket match ---');
    simulateMatchCompletion(bracket, match1.id, match1.participant1Id, match1.participant2Id);
    displayBracketStructure(bracket);
    
    console.log('\n--- STEP 2: Complete second upper bracket match ---');
    simulateMatchCompletion(bracket, match2.id, match2.participant1Id, match2.participant2Id);
    displayBracketStructure(bracket);
    
    // Check if lower bracket matches are properly set up
    const lowerBracketMatches = Object.values(bracket.matchesById).filter(match => !match.isUpperBracket && !match.identifier?.includes('Grand Final'));
    const pendingLowerMatches = lowerBracketMatches.filter(match => match.status === 'PENDING');
    
    console.log(`\n--- PROGRESSION ANALYSIS ---`);
    console.log(`Lower bracket matches: ${lowerBracketMatches.length}`);
    console.log(`Pending lower bracket matches: ${pendingLowerMatches.length}`);

    if (pendingLowerMatches.length > 0) {
        console.log('✅ Lower bracket progression is working - matches are becoming PENDING');
        pendingLowerMatches.forEach(match => {
            const p1 = bracket.participants.find(p => p.id === match.participant1Id);
            const p2 = bracket.participants.find(p => p.id === match.participant2Id);
            console.log(`  ${match.id}: ${p1?.name || match.participant1Id} vs ${p2?.name || match.participant2Id}`);
        });
    } else {
        console.log('❌ Lower bracket progression may have issues - no matches became PENDING');
    }

    // Test Winners Semi-Finals progression
    console.log('\n--- STEP 3: Complete Winners Semi-Finals to test LBR3 progression ---');
    const winnersSemiFinal = bracket.matchesById['UBR2M5'];
    if (winnersSemiFinal && winnersSemiFinal.participant1Id && winnersSemiFinal.participant2Id) {
        simulateMatchCompletion(bracket, winnersSemiFinal.id, winnersSemiFinal.participant1Id, winnersSemiFinal.participant2Id);

        // Check if LBR3 match received the loser
        const lbr3Match = bracket.matchesById['LBR3M11'];
        if (lbr3Match) {
            const p1 = bracket.participants.find(p => p.id === lbr3Match.participant1Id);
            const p2 = bracket.participants.find(p => p.id === lbr3Match.participant2Id);
            console.log(`\n✅ LBR3 progression check: ${lbr3Match.id} = ${p1?.name || lbr3Match.participant1Id} vs ${p2?.name || lbr3Match.participant2Id}`);

            if (!lbr3Match.participant1Id?.startsWith('TBD_') && !lbr3Match.participant2Id?.startsWith('TBD_')) {
                console.log('✅ LBR3 match is ready with both participants set!');
            } else {
                console.log('⚠️  LBR3 match still has TBD placeholders');
            }
        }
    }
} else {
    console.log('❌ Could not find sufficient upper bracket matches to test');
}

console.log('\n=== TEST COMPLETE ===');
