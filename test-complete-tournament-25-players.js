// Complete 25-Player Double Elimination Tournament Test
// This test runs a full tournament from start to finish to verify all logic

const { generateBracket } = require('./functions/bracketGenerator');

console.log('=== COMPLETE 25-PLAYER DOUBLE ELIMINATION TOURNAMENT TEST ===\n');

// Generate 25 participants
const participants = [];
for (let i = 1; i <= 25; i++) {
    participants.push({
        id: `0x${i.toString().padStart(40, '0')}`,
        name: `Player ${i.toString().padStart(2, '0')}`,
        walletAddress: `0x${i.toString().padStart(40, '0')}`,
        seed: i
    });
}

console.log(`Generated ${participants.length} participants`);

// Generate initial bracket
console.log('Generating 25-player double elimination bracket...');
const bracket = generateBracket(participants, 'double-elimination', { matchFormat: 'bo1' });

console.log(`✅ Bracket generated with ${Object.keys(bracket.matchesById).length} matches`);
console.log(`Upper bracket rounds: ${bracket.metadata.numUpperRounds}`);
console.log(`Lower bracket rounds: ${bracket.metadata.numLowerRounds}`);

// Tournament state tracking
let tournamentComplete = false;
let tournamentWinner = null;
let matchesCompleted = 0;
let roundsCompleted = 0;

// Simulate match completion with progression logic
function completeMatch(bracket, matchId, winnerId, loserId) {
    const { matchesById } = bracket;
    const completedMatch = matchesById[matchId];
    
    if (!completedMatch) {
        console.error(`❌ Match ${matchId} not found!`);
        return false;
    }
    
    if (completedMatch.status === 'COMPLETED' || completedMatch.status === 'BYE') {
        console.log(`⚠️  Match ${matchId} already completed`);
        return false;
    }
    
    // Update match status
    completedMatch.status = 'COMPLETED';
    completedMatch.winnerId = winnerId;
    completedMatch.loserId = loserId;
    
    console.log(`  ✅ ${matchId}: ${getParticipantName(winnerId)} defeats ${getParticipantName(loserId)}`);
    
    // Apply progression logic
    if (completedMatch.isUpperBracket) {
        // Handle winner progression
        const winnerNextMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;
        if (winnerNextMatch) {
            if (winnerNextMatch.participant1Id === null || winnerNextMatch.participant1Id === `TBD_${completedMatch.id}`) {
                winnerNextMatch.participant1Id = winnerId;
            } else if (winnerNextMatch.participant2Id === null || winnerNextMatch.participant2Id === `TBD_${completedMatch.id}`) {
                winnerNextMatch.participant2Id = winnerId;
            }
            
            // Update status if both participants are now set
            if (winnerNextMatch.participant1Id && winnerNextMatch.participant2Id &&
                !winnerNextMatch.participant1Id.startsWith('TBD_') &&
                !winnerNextMatch.participant2Id.startsWith('TBD_')) {
                winnerNextMatch.status = 'PENDING';
            }
        }
        
        // Handle loser progression
        const loserNextMatch = completedMatch.nextLoserMatchId ? matchesById[completedMatch.nextLoserMatchId] : null;
        if (loserNextMatch && loserId && loserId !== 'BYE') {
            if (loserNextMatch.participant1Id === null || loserNextMatch.participant1Id === `TBD_${completedMatch.id}`) {
                loserNextMatch.participant1Id = loserId;
            } else if (loserNextMatch.participant2Id === null || loserNextMatch.participant2Id === `TBD_${completedMatch.id}`) {
                loserNextMatch.participant2Id = loserId;
            }
            
            // Update status if both participants are now set
            if (loserNextMatch.participant1Id && loserNextMatch.participant2Id &&
                !loserNextMatch.participant1Id.startsWith('TBD_') &&
                !loserNextMatch.participant2Id.startsWith('TBD_') &&
                loserNextMatch.participant1Id !== 'BYE' && loserNextMatch.participant2Id !== 'BYE') {
                loserNextMatch.status = 'PENDING';
            }
        }
    } else {
        // Handle lower bracket progression
        const nextLowerMatch = completedMatch.nextMatchId ? matchesById[completedMatch.nextMatchId] : null;
        if (nextLowerMatch) {
            if (nextLowerMatch.participant1Id === null || nextLowerMatch.participant1Id === `TBD_${completedMatch.id}`) {
                nextLowerMatch.participant1Id = winnerId;
            } else if (nextLowerMatch.participant2Id === null || nextLowerMatch.participant2Id === `TBD_${completedMatch.id}`) {
                nextLowerMatch.participant2Id = winnerId;
            }
            
            // Update status if both participants are now set
            if (nextLowerMatch.participant1Id && nextLowerMatch.participant2Id &&
                !nextLowerMatch.participant1Id.startsWith('TBD_') &&
                !nextLowerMatch.participant2Id.startsWith('TBD_') &&
                nextLowerMatch.participant1Id !== 'BYE' && nextLowerMatch.participant2Id !== 'BYE') {
                nextLowerMatch.status = 'PENDING';
            }
        } else {
            // Check if this is a grand final match
            if (completedMatch.identifier === "Grand Final") {
                const resetMatch = Object.values(matchesById).find(match =>
                    match.identifier === "Grand Final Reset"
                );
                
                if (resetMatch) {
                    const lowerBracketWinnerWon = completedMatch.winnerId === completedMatch.participant2Id;
                    
                    if (lowerBracketWinnerWon) {
                        // Set up reset match
                        resetMatch.participant1Id = completedMatch.participant1Id;
                        resetMatch.participant2Id = completedMatch.participant2Id;
                        resetMatch.status = 'PENDING';
                        console.log(`  🔄 Grand Final Reset triggered!`);
                    } else {
                        // Tournament complete
                        tournamentComplete = true;
                        tournamentWinner = completedMatch.winnerId;
                        console.log(`  🏆 TOURNAMENT COMPLETE! Winner: ${getParticipantName(tournamentWinner)}`);
                    }
                } else {
                    tournamentComplete = true;
                    tournamentWinner = completedMatch.winnerId;
                    console.log(`  🏆 TOURNAMENT COMPLETE! Winner: ${getParticipantName(tournamentWinner)}`);
                }
            } else if (completedMatch.identifier === "Grand Final Reset") {
                tournamentComplete = true;
                tournamentWinner = completedMatch.winnerId;
                console.log(`  🏆 TOURNAMENT COMPLETE! Winner: ${getParticipantName(tournamentWinner)}`);
            }
        }
    }
    
    return true;
}

function getParticipantName(participantId) {
    if (!participantId) return 'TBD';
    if (participantId === 'BYE') return 'BYE';
    if (participantId.startsWith('TBD_')) return participantId;
    
    const participant = participants.find(p => p.id === participantId);
    return participant ? participant.name : participantId.substring(0, 8) + '...';
}

function getAvailableMatches(bracket) {
    return Object.values(bracket.matchesById).filter(match => {
        if (match.status === 'COMPLETED' || match.status === 'BYE') return false;

        // Handle BYE matches first
        if (match.participant1Id === 'BYE' || match.participant2Id === 'BYE') {
            return true; // BYE matches can be completed immediately
        }

        // Check if match has both participants and neither is a TBD placeholder
        const hasP1 = match.participant1Id && !match.participant1Id.startsWith('TBD_');
        const hasP2 = match.participant2Id && !match.participant2Id.startsWith('TBD_');

        // Special case: if one participant is null and the other is real, this might need a bye
        if ((match.participant1Id === null && hasP2) || (match.participant2Id === null && hasP1)) {
            // Check if this is the last match in a round that needs a bye
            return true;
        }

        return hasP1 && hasP2;
    });
}

function completeBYEMatches(bracket) {
    const byeMatches = Object.values(bracket.matchesById).filter(match => 
        (match.participant1Id === 'BYE' || match.participant2Id === 'BYE') && 
        match.status !== 'COMPLETED' && match.status !== 'BYE'
    );
    
    byeMatches.forEach(match => {
        if (match.participant1Id === 'BYE') {
            completeMatch(bracket, match.id, match.participant2Id, 'BYE');
        } else if (match.participant2Id === 'BYE') {
            completeMatch(bracket, match.id, match.participant1Id, 'BYE');
        }
        matchesCompleted++;
    });
}

// Helper function to simulate match result (winner is randomly chosen)
function simulateMatchResult(match) {
    const p1 = match.participant1Id;
    const p2 = match.participant2Id;

    if (p1 === 'BYE') return { winnerId: p2, loserId: 'BYE' };
    if (p2 === 'BYE') return { winnerId: p1, loserId: 'BYE' };

    // Handle cases where one participant is null (needs bye)
    if (p1 === null && p2 && p2 !== 'BYE') {
        // Give p2 a bye
        match.participant1Id = 'BYE';
        return { winnerId: p2, loserId: 'BYE' };
    }
    if (p2 === null && p1 && p1 !== 'BYE') {
        // Give p1 a bye
        match.participant2Id = 'BYE';
        return { winnerId: p1, loserId: 'BYE' };
    }

    // Random winner for simulation
    const winner = Math.random() < 0.5 ? p1 : p2;
    const loser = winner === p1 ? p2 : p1;

    return { winnerId: winner, loserId: loser };
}

console.log('\n=== RUNNING COMPLETE TOURNAMENT ===');

// Complete all BYE matches first
console.log('\n--- Completing BYE matches ---');
completeBYEMatches(bracket);

let roundNumber = 1;
while (!tournamentComplete) {
    const availableMatches = getAvailableMatches(bracket);
    
    if (availableMatches.length === 0) {
        console.log('\n❌ No available matches found - tournament may be stuck');
        break;
    }
    
    console.log(`\n--- Round ${roundNumber} (${availableMatches.length} matches available) ---`);
    
    // Complete all available matches in this round
    let matchesInRound = 0;
    availableMatches.forEach(match => {
        const result = simulateMatchResult(match);
        if (completeMatch(bracket, match.id, result.winnerId, result.loserId)) {
            matchesCompleted++;
            matchesInRound++;
        }
    });
    
    console.log(`  Completed ${matchesInRound} matches this round`);
    
    // Complete any new BYE matches that became available
    completeBYEMatches(bracket);
    
    roundNumber++;
    roundsCompleted++;
    
    // Safety check to prevent infinite loops
    if (roundNumber > 50) {
        console.log('\n❌ Tournament exceeded maximum rounds - stopping');
        break;
    }
}

console.log('\n=== TOURNAMENT RESULTS ===');
console.log(`Tournament Complete: ${tournamentComplete ? '✅ YES' : '❌ NO'}`);
console.log(`Tournament Winner: ${tournamentWinner ? getParticipantName(tournamentWinner) : 'None'}`);
console.log(`Total Matches Completed: ${matchesCompleted}`);
console.log(`Total Rounds: ${roundsCompleted}`);
console.log(`Expected Total Matches: ${Object.keys(bracket.matchesById).length}`);

// Verify all matches are completed
const incompleteMatches = Object.values(bracket.matchesById).filter(match => 
    match.status !== 'COMPLETED' && match.status !== 'BYE'
);

if (incompleteMatches.length === 0) {
    console.log('✅ All matches completed successfully');
} else {
    console.log(`❌ ${incompleteMatches.length} matches remain incomplete:`);
    incompleteMatches.forEach(match => {
        console.log(`  ${match.id}: ${getParticipantName(match.participant1Id)} vs ${getParticipantName(match.participant2Id)} [${match.status}]`);
    });
}

console.log('\n=== TEST COMPLETE ===');
