<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberField - Ronin Tournaments </title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script>
        // Ensure React is properly initialized
        window.React = React;
        window.ReactDOM = ReactDOM;

        // Log React versions for debugging
        console.log('React version:', React.version);
        console.log('ReactDOM version:', ReactDOM.version);
    </script>
    <script src="/src/bracket-bundle.js"></script>
    <script src="/src/admin-bundle.js"></script>
    <script src="/src/notification-bundle.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <style>
        :root {
            --bg-deep-space: #080815;
            --bg-element-dark: #101022;
            --bg-element-medium: #181830;
            --accent-cyan: #60EFFF;
            --accent-magenta: #F85AFF;
            --accent-blue: #5A78FF;
            --text-primary: #E8E8FF;
            --text-secondary: #A0A0CC;
            --border-cyber: #3A3A7A;
            --border-cyber-glow: #5A78FF;
            --accent-blue-rgb: 90, 120, 255;
            --accent-cyan-rgb: 96, 239, 255;
            --accent-magenta-rgb: 248, 90, 255;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-deep-space);
            color: var(--text-primary);
            overflow-x: hidden;
            background-image:
                linear-gradient(rgba(58, 58, 122, 0.08) 1px, transparent 1px),
                linear-gradient(90deg, rgba(58, 58, 122, 0.08) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        h1, h2, h3, .font-orbitron {
            font-family: 'Orbitron', sans-serif;
            letter-spacing: 0.5px;
        }

        .page-section { display: none; } /* Hide all pages by default */
        .page-section.active { display: block; } /* Show active page */

        .header-cyber {
            background-color: rgba(8, 8, 21, 0.7);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid var(--border-cyber);
            box-shadow: 0 2px 20px rgba(0,0,0,0.3);
        }
        .app-title {
            font-weight: 700;
            text-shadow: 0 0 10px var(--accent-cyan), 0 0 20px var(--accent-cyan);
        }

        /* Search Bar & Form Inputs */
        .input-cyber {
            background-color: var(--bg-element-medium);
            border: 1px solid var(--border-cyber);
            color: var(--text-primary);
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.3), 0 0 8px rgba(var(--accent-blue-rgb), 0.3);
            transition: all 0.3s ease;
            border-radius: 0.375rem; /* rounded-md */
            padding: 0.75rem 1rem; /* py-3 px-4 */
            width: 100%;
        }
        .input-cyber::placeholder { color: var(--text-secondary); }
        .input-cyber:focus {
            border-color: var(--accent-blue);
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.3), 0 0 15px rgba(var(--accent-blue-rgb), 0.6);
            background-color: var(--bg-element-dark);
            outline: none;
        }
        textarea.input-cyber { min-height: 120px; }
        select.input-cyber { appearance: none; background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='2' stroke='%2360EFFF' class='w-6 h-6'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='m19.5 8.25-7.5 7.5-7.5-7.5' /%3E%3C/svg%3E"); background-repeat: no-repeat; background-position: right 0.75rem center; background-size: 1.25em; padding-right: 2.5rem;}


        .section-title {
            color: var(--accent-magenta);
            font-weight: 700;
            text-shadow: 0 0 8px rgba(var(--accent-magenta-rgb), 0.6);
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--accent-magenta);
            display: inline-block;
            margin-bottom: 1.25rem;
        }

        .tournament-card-cyber {
            background: linear-gradient(145deg, var(--bg-element-medium), var(--bg-element-dark));
            border: 1px solid var(--border-cyber);
            border-radius: 0.75rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3), 0 0 10px rgba(var(--accent-blue-rgb), 0.15), inset 0 0 3px rgba(var(--accent-cyan-rgb),0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
        }
        .tournament-card-cyber:hover {
            transform: translateY(-6px) scale(1.01);
            border-color: var(--accent-blue);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4), 0 0 20px rgba(var(--accent-blue-rgb), 0.35), inset 0 0 5px rgba(var(--accent-cyan-rgb),0.2);
        }
        .tournament-card-cyber img.card-banner {
            border-top-left-radius: 0.65rem;
            border-top-right-radius: 0.65rem;
        }

        .game-icon-card-cyber {
            background-color: var(--bg-element-medium);
            border: 1px solid var(--border-cyber);
            border-radius: 0.75rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2), 0 0 8px rgba(var(--accent-cyan-rgb), 0.2);
            transition: transform 0.2s ease, box-shadow 0.2s ease, border-color 0.2s ease;
        }
        .game-icon-card-cyber:hover {
            transform: scale(1.08) rotate(-2deg);
            border-color: var(--accent-cyan);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3), 0 0 15px rgba(var(--accent-cyan-rgb), 0.4);
        }

        .btn {
            font-family: 'Orbitron', sans-serif;
            font-weight: 500;
            letter-spacing: 1px;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem; /* Default padding */
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            filter: grayscale(50%);
        }
        .btn-cyber-primary {
            background: linear-gradient(to right, var(--accent-blue), var(--accent-magenta));
            color: white;
            border: 1px solid var(--accent-magenta);
            box-shadow: 0 2px 8px rgba(var(--accent-magenta-rgb), 0.4), 0 0 15px rgba(var(--accent-blue-rgb), 0.2);
        }
        .btn-cyber-primary:hover:not(:disabled) {
            box-shadow: 0 4px 12px rgba(var(--accent-magenta-rgb), 0.6), 0 0 25px rgba(var(--accent-blue-rgb), 0.4);
            filter: brightness(1.15);
            transform: translateY(-2px);
        }
        .btn-cyber-secondary {
            background-color: transparent;
            color: var(--accent-cyan);
            border: 1px solid var(--accent-cyan);
        }
        .btn-cyber-secondary:hover:not(:disabled) {
            background-color: rgba(var(--accent-cyan-rgb), 0.15);
            color: white;
            border-color: white;
            box-shadow: 0 0 10px rgba(var(--accent-cyan-rgb), 0.5);
            transform: translateY(-1px);
        }
        .btn-connect-wallet {
            background-color: var(--bg-element-medium);
            color: var(--accent-cyan);
            border: 1px solid var(--border-cyber-glow);
            box-shadow: 0 0 10px rgba(var(--accent-blue-rgb), 0.5);
            padding: 0.6rem 1rem; /* Specific padding */
        }
        .btn-connect-wallet:hover:not(:disabled) {
            background-color: var(--bg-element-dark);
            border-color: var(--accent-cyan);
            box-shadow: 0 0 15px rgba(var(--accent-cyan-rgb), 0.7);
            color: white;
        }
        .wallet-address-display {
            background-color: var(--bg-element-medium);
            color: var(--accent-cyan);
            border: 1px solid var(--border-cyber-glow);
            padding: 0.6rem 1rem;
            border-radius: 0.375rem;
            box-shadow: 0 0 8px rgba(var(--accent-blue-rgb), 0.4);
            font-family: 'Orbitron', sans-serif;
            font-weight: 500;
        }

        .bottom-nav-cyber {
            background-color: rgba(8, 8, 21, 0.85);
            backdrop-filter: blur(12px);
            border-top: 1px solid var(--border-cyber);
            box-shadow: 0 -2px 20px rgba(0,0,0,0.3);
            height: 5rem; /* 80px */
        }
        .bottom-nav-cyber a {
            transition: color 0.2s ease, transform 0.2s ease;
            color: var(--text-secondary);
        }
        .bottom-nav-cyber a:hover {
            transform: translateY(-2px);
            color: var(--accent-cyan);
        }
        .bottom-nav-cyber a.active svg, .bottom-nav-cyber a.active span {
            color: var(--accent-cyan);
            filter: drop-shadow(0 0 8px var(--accent-cyan));
        }
        .bottom-nav-cyber .create-btn-wrapper {
             transform: translateY(-10px);
        }
        .bottom-nav-cyber .create-btn-glow {
            background: linear-gradient(45deg, var(--accent-magenta), var(--accent-cyan));
            box-shadow: 0 0 18px rgba(var(--accent-magenta-rgb),0.6), 0 0 25px rgba(var(--accent-cyan-rgb),0.5);
            border: 2px solid rgba(255,255,255,0.5);
            transition: all 0.3s ease;
            width: 64px; height: 64px;
            display: flex; align-items: center; justify-content: center;
        }
        .bottom-nav-cyber .create-btn-glow:hover {
            filter: brightness(1.2);
            transform: scale(1.05);
            box-shadow: 0 0 25px rgba(var(--accent-magenta-rgb),0.8), 0 0 35px rgba(var(--accent-cyan-rgb),0.7);
        }

        .horizontal-scroll-cyber::-webkit-scrollbar { height: 6px; }
        .horizontal-scroll-cyber::-webkit-scrollbar-track { background: var(--bg-element-dark); border-radius: 3px; }
        .horizontal-scroll-cyber::-webkit-scrollbar-thumb { background: var(--border-cyber-glow); border-radius: 3px; }
        .horizontal-scroll-cyber::-webkit-scrollbar-thumb:hover { background: var(--accent-cyan); }
        .horizontal-scroll-cyber { scrollbar-width: thin; scrollbar-color: var(--border-cyber-glow) var(--bg-element-dark); }

        .tag-cyber { background-color: rgba(var(--accent-blue-rgb), 0.2); color: var(--accent-blue); border: 1px solid rgba(var(--accent-blue-rgb), 0.5); padding: 0.25rem 0.6rem; font-weight: 500; border-radius: 9999px; }
        .tag-cyber-green { background-color: rgba(60, 200, 130, 0.2); color: #80FFC0; border: 1px solid rgba(60, 200, 130, 0.5); padding: 0.25rem 0.6rem; font-weight: 500; border-radius: 9999px; }
        .tag-cyber-red { background-color: rgba(255, 80, 80, 0.2); color: #FFB0B0; border: 1px solid rgba(255, 80, 80, 0.5); padding: 0.25rem 0.6rem; font-weight: 500; border-radius: 9999px; }

        .modal-backdrop {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(8, 8, 21, 0.8); backdrop-filter: blur(5px);
            display: flex; justify-content: center; align-items: center;
            z-index: 100; opacity: 0; visibility: hidden; transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .modal-backdrop.active { opacity: 1; visibility: visible; }
        .modal-content {
            background-color: var(--bg-element-medium);
            padding: 2rem; border-radius: 0.5rem; border: 1px solid var(--border-cyber-glow);
            box-shadow: 0 0 30px rgba(var(--accent-blue-rgb), 0.5);
            color: var(--text-primary); width: 90%; max-width: 400px; text-align: center;
        }
        .modal-content p { font-size: 1.1rem; line-height: 1.6; }
        .modal-content button { margin-top: 1.5rem; }

        /* Form specific styling */
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--accent-cyan);
            font-weight: 500;
            font-size: 0.9rem;
            text-transform: uppercase;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid;
            margin-bottom: 1rem;
        }

        .alert-info {
            background-color: rgba(96, 239, 255, 0.1);
            border-color: var(--accent-cyan);
            color: var(--accent-cyan);
        }

        .text-muted {
            color: #888;
            font-size: 0.85rem;
        }

        /* Tournament Details Specific */
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }
        .details-card {
            background-color: var(--bg-element-dark);
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid var(--border-cyber);
        }
        .details-card h3 {
            color: var(--accent-magenta);
            margin-bottom: 0.75rem;
            font-size: 1.25rem;
        }
        .details-card p {
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }
        .details-card p strong {
            color: var(--text-primary);
        }
        .embedded-stream iframe {
            border: 2px solid var(--border-cyber-glow);
            border-radius: 0.5rem;
            box-shadow: 0 0 15px rgba(var(--accent-cyan-rgb), 0.3);
        }
        /* Styling for tournament lists on homepage */
        #featuredTournamentsContainer .tournament-card-cyber,
        #upcomingTournamentsContainer .tournament-card-cyber {
            margin-bottom: 1.5rem; /* space-y-6 equivalent for dynamic content */
        }

    </style>
</head>
<body class="pb-28">

    <header class="p-4 flex justify-between items-center sticky top-0 z-50 header-cyber">
        <h1 class="text-3xl app-title text-cyan-300 cursor-pointer" data-navigate="home">CyberField</h1>
        <div id="wallet-section">
            <button id="connectWalletBtn" class="btn btn-connect-wallet text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5"><path stroke-linecap="round" stroke-linejoin="round" d="M21 12a2.25 2.25 0 0 0-2.25-2.25H15a3 3 0 1 1-6 0H5.25A2.25 2.25 0 0 0 3 12m18 0v6.75A2.25 2.25 0 0 1 18.75 21H5.25A2.25 2.25 0 0 1 3 18.75V12m18 0V9.75A2.25 2.25 0 0 0 18.75 7.5H5.25A2.25 2.25 0 0 0 3 9.75v2.25" /></svg>
                Connect Wallet
            </button>
            <div id="walletInfo" class="hidden wallet-address-display text-sm">
                <span id="walletAddress"></span>
            </div>
        </div>
    </header>

    <div id="pageContainer">

        <section id="home" class="page-section active">
            <main class="p-5 space-y-10">
                <div class="relative">
                    <input type="text" id="searchInput" placeholder="Search tournaments, games, creators..." class="input-cyber pl-14 pr-5 py-3.5 text-lg">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="var(--accent-magenta)" class="w-6 h-6 opacity-80"><path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" /></svg>
                    </div>
                </div>
                <div>
                    <h2 class="text-2xl section-title">Featured Tournaments</h2>
                    <div id="featuredTournamentsContainer" class="space-y-6">
                        <p id="featuredLoading" class="text-gray-400">Loading featured tournaments...</p>
                    </div>
                </div>
                <div>
                    <h2 class="text-2xl section-title">Browse by Game</h2>
                    <div id="browseByGameContainer" class="flex space-x-5 overflow-x-auto pb-4 horizontal-scroll-cyber">
                        <div class="flex-shrink-0 w-32 text-center game-icon-card-cyber p-5 rounded-xl aspect-square flex items-center justify-center mb-2.5 cursor-pointer" data-game-filter="Axie Infinity: Origins">
                            <img src="/public/axieInfinityOrigins.png" alt="Axie Infinity: Origins" class="h-16 w-16 object-contain rounded-md"><p class="text-sm truncate text-cyan-200 font-medium sr-only">Axie Infinity: Origins</p>
                        </div>
                        <div class="flex-shrink-0 w-32 text-center game-icon-card-cyber p-5 rounded-xl aspect-square flex items-center justify-center mb-2.5 cursor-pointer" data-game-filter="Axie Infinity: Classic">
                            <img src="/public/axieInfinityClassic.png" alt="Axie Infinity: Classic" class="h-16 w-16 object-contain rounded-md"><p class="text-sm truncate text-cyan-200 font-medium sr-only">Axie Infinity: Classic</p>
                        </div>
                        <div class="flex-shrink-0 w-32 text-center game-icon-card-cyber p-5 rounded-xl aspect-square flex items-center justify-center mb-2.5 cursor-pointer" data-game-filter="Moshi Admirals">
                            <img src="/public/moshiAdmirals.png" alt="Moshi Admirals" class="h-16 w-16 object-contain rounded-md"><p class="text-sm truncate text-cyan-200 font-medium sr-only">Moshi Admirals</p>
                        </div>
                    </div>
                </div>
                <div>
                    <h2 class="text-2xl section-title">Upcoming Tournaments</h2>
                    <div id="upcomingTournamentsContainer" class="space-y-6">
                        <p id="upcomingLoading" class="text-gray-400">Loading upcoming tournaments...</p>
                    </div>
                </div>
            </main>
        </section>

        <section id="create-tournament" class="page-section">
            <main class="p-5 space-y-8 max-w-3xl mx-auto">
                <h2 class="text-3xl section-title text-center">Create New Tournament</h2>
                <form id="createTournamentForm" class="space-y-6 tournament-card-cyber p-6 md:p-8 rounded-lg">
                    <div class="form-group">
                        <label for="tournamentName" class="form-label">Tournament Name</label>
                        <input type="text" id="tournamentName" name="tournamentName" class="input-cyber" placeholder="e.g., Weekly Ronin Rumble" required>
                    </div>
                    <div class="form-group">
                        <label for="gameName" class="form-label">Game</label>
                        <input type="text" id="gameName" name="gameName" class="input-cyber" placeholder="e.g., Axie Infinity: Origins" list="commonGames" required>
                        <datalist id="commonGames">
                            <option value="Axie Infinity: Origins">
                            <option value="Axie Infinity: Classic">
                            <option value="Moshi Admirals">
                            <option value="Apeiron">
                            <option value="Project Eri Guild">
                            <option value="Tribally Guild">
                             </datalist>
                    </div>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="tournamentFormat" class="form-label">Format</label>
                            <select id="tournamentFormat" name="tournamentFormat" class="input-cyber" required>
                                <option value="">Select Format...</option>
                                <option value="single-elim">Single Elimination</option>
                                <option value="double-elim">Double Elimination</option>
                                <option value="round-robin">Round Robin</option>
                                <!-- <option value="2v2">2v2 Teams</option> -->
                                <!-- <option value="3v3">3v3 Teams</option> -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="matchFormat" class="form-label">Match Format</label>
                            <select id="matchFormat" name="matchFormat" class="input-cyber" required>
                                <option value="">Best of...</option>
                                <option value="bo1">Best of 1</option>
                                <option value="bo3">Best of 3</option>
                                <option value="bo5">Best of 5</option>
                                <option value="bo7">Best of 7</option>
                            </select>
                        </div>
                    </div>

                    <!-- Team Tournament Info (shown only for 2v2/3v3) -->
                    <div id="teamTournamentInfo" class="form-group" style="display: none;">
                        <div class="p-4 border border-cyan-700/30 rounded-lg bg-cyan-900/10">
                            <h4 class="text-cyan-300 font-semibold mb-2">Team Tournament</h4>
                            <p class="text-sm text-gray-300 mb-3">
                                Players will form teams when they join the tournament. They can specify their teammates and team name,
                                or be automatically grouped with other solo players.
                            </p>
                            <div class="form-group">
                                <label for="bracketFormat" class="form-label">Bracket Format</label>
                                <select id="bracketFormat" name="bracketFormat" class="input-cyber">
                                    <option value="single-elim">Single Elimination</option>
                                    <option value="double-elim">Double Elimination</option>
                                    <option value="round-robin">Round Robin</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="entryFee" class="form-label">Entry Fee (e.g., 0 for Free, 0.5 RON, 10 AXS)</label>
                        <input type="text" id="entryFee" name="entryFee" class="input-cyber" placeholder="0 or 0.5 RON" required>
                    </div>
                    <div class="form-group">
                        <label for="prizePool" class="form-label">Prize Pool Description</label>
                        <input type="text" id="prizePool" name="prizePool" class="input-cyber" placeholder="e.g., 1000 AXS, Winner takes all, Top 3 split" required>
                    </div>
                     <div class="grid md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="startDate" class="form-label">Start Date & Time (Your Local Time)</label>
                            <input type="datetime-local" id="startDate" name="startDate" class="input-cyber" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="maxEntries" class="form-label">Max Entries (0 for unlimited)</label>
                        <input type="number" id="maxEntries" name="maxEntries" class="input-cyber" placeholder="e.g., 64 or 0" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label for="rules" class="form-label">Tournament Rules</label>
                        <textarea id="rules" name="rules" class="input-cyber" placeholder="Describe the rules for your tournament..."></textarea>
                    <div class="form-group">
                        <label for="rules" class="form-label">Tournament Rules</label>
                        <input type="url" id="streamUrl" name="streamUrl" class="input-cyber" placeholder="https://www.twitch.tv/yourchannel or https://www.youtube.com/watch?v=VIDEO_ID">
                    </div>
                     <div class="form-group">
                        <label for="bannerImageUrl" class="form-label">Banner Image URL (Optional)</label>
                        <input type="url" id="bannerImageUrl" name="bannerImageUrl" class="input-cyber" placeholder="https://example.com/banner.jpg">
                    </div>
                    <div class="pt-4 text-center">
                        <button type="submit" id="submitCreateTournamentBtn" class="btn btn-cyber-primary px-8 py-3 text-lg">Create Tournament</button>
                    </div>
                </form>
            </main>
        </section>

        <section id="tournament-details" class="page-section">
            <main class="p-5 space-y-8 max-w-4xl mx-auto">
                <div id="tournamentDetailsContent" class="space-y-6">
                    <img id="detailsBannerImage" src="https://placehold.co/800x300/0A0A1A/60EFFF?text=Tournament+Banner" alt="Tournament Banner" class="w-full h-64 object-cover rounded-lg mb-4 opacity-80 card-banner" style="display:none;">
                    <h2 class="text-3xl section-title text-center" id="detailsTournamentName">Tournament Title Loading...</h2>

                    <div class="details-grid">
                        <div class="details-card">
                            <h3><i class="fas fa-info-circle mr-2 text-cyan-400"></i>Overview</h3>
                            <p><strong>Game:</strong> <span id="detailsGameName">N/A</span></p>
                            <p><strong>Format:</strong> <span id="detailsTournamentFormat">N/A</span> (Best of <span id="detailsMatchFormat">N/A</span>)</p>
                            <p><strong>Starts:</strong> <span id="detailsStartDate">N/A</span></p>
                            <p><strong>Status:</strong> <span id="detailsStatus" class="tag-cyber">N/A</span></p>
                            <div id="countdownTimer" class="mt-4 p-3 bg-cyan-900/20 rounded-lg border border-cyan-700/30" style="display: none;">
                                <p class="text-sm text-cyan-300 mb-1">Tournament starts in:</p>
                                <p class="text-xl font-orbitron text-cyan-200" id="countdownDisplay">--:--:--</p>
                            </div>
                        </div>
                        <div class="details-card">
                            <h3><i class="fas fa-coins mr-2 text-yellow-400"></i>Entry & Prize</h3>
                            <p><strong>Entry Fee:</strong> <span id="detailsEntryFee">N/A</span></p>
                            <p><strong>Creator Wallet:</strong> <span id="detailsCreatorAddress" class="text-xs break-all">N/A</span></p>
                            <p><strong>Prize Pool:</strong> <span id="detailsPrizePool">N/A</span></p>
                            <p><strong>Participants:</strong> <span id="detailsParticipants">N/A</span> / <span id="detailsMaxEntries">N/A</span></p>
                        </div>
                    </div>

                    <div class="details-card" id="detailsStreamSection" style="display: none;">
                        <h3><i class="fas fa-video mr-2 text-red-500"></i>Live Stream</h3>
                        <div class="embedded-stream aspect-video" id="detailsStreamEmbed">
                        </div>
                    </div>

                    <div class="details-card">
                        <h3><i class="fas fa-gavel mr-2 text-gray-400"></i>Rules</h3>
                        <p id="detailsRules" class="whitespace-pre-wrap">No rules provided.</p>
                    </div>

                     <div class="details-card">
                        <h3><i class="fas fa-users mr-2 text-purple-400"></i>Participants (<span id="participantsCountDisplay">0</span>)</h3>
                        <div id="participantsList" class="flex flex-wrap gap-2">
                            <p class="w-full text-sm text-gray-400">Participants list will appear here...</p>
                        </div>
                    </div>

                    <div class="details-card" id="adminControlSection" style="display: none;">
                        <h3><i class="fas fa-cog mr-2 text-red-400"></i>Tournament Administration</h3>
                        <div id="adminControlContainer" class="mt-4">
                            <!-- Admin controls will be rendered here -->
                        </div>
                        <!-- Debug button for testing admin controls -->
                        <button id="debugAdminBtn" class="btn btn-cyber-secondary mt-2" style="display: none;" onclick="debugAdminControls()">
                            Debug Admin Controls
                        </button>
                    </div>

                    <div class="details-card" id="bracketSection" style="display: none;">
                        <h3><i class="fas fa-trophy mr-2 text-yellow-400"></i>Tournament Bracket</h3>
                        <div id="bracketContainer" class="mt-4">
                            <!-- Bracket will be rendered here -->
                        </div>
                    </div>

                    <div class="text-center py-6">
                        <button id="joinTournamentBtn" class="btn btn-cyber-primary px-10 py-4 text-xl">Join Tournament</button>
                    </div>
                </div>
            </main>
        </section>

        <!-- Team Formation Modal for 2v2/3v3 tournaments -->
        <div id="teamFormationModal" class="modal-backdrop">
            <div class="modal-content max-w-md mx-auto">
                <div class="modal-header">
                    <h3 class="text-xl font-bold text-cyan-300">Join Team Tournament</h3>
                    <button id="closeTeamModal" class="text-gray-400 hover:text-white text-2xl">&times;</button>
                </div>
                <div class="modal-body space-y-4">
                    <div class="space-y-4">
                        <div class="flex items-center space-x-4">
                            <input type="radio" id="joinWithTeam" name="joinType" value="team" class="text-cyan-400" checked>
                            <label for="joinWithTeam" class="text-sm">Join with specific teammates</label>
                        </div>
                        <div class="flex items-center space-x-4">
                            <input type="radio" id="joinSolo" name="joinType" value="solo" class="text-cyan-400">
                            <label for="joinSolo" class="text-sm">Join solo (auto-matched with other solo players)</label>
                        </div>
                    </div>

                    <!-- Team Formation Interface -->
                    <div id="teamFormInterface" class="space-y-4">
                        <div class="form-group">
                            <label class="form-label text-sm">Team Name (Optional)</label>
                            <input type="text" id="teamNameInput" class="input-cyber" placeholder="Enter team name">
                        </div>
                        <div id="teammateInputs" class="space-y-3">
                            <!-- Teammate inputs will be added here dynamically -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="confirmJoinTeam" class="btn btn-cyber-primary px-6 py-2">Join Tournament</button>
                    <button id="cancelJoinTeam" class="btn btn-cyber-secondary px-6 py-2">Cancel</button>
                </div>
            </div>
        </div>

        <section id="my-tournaments" class="page-section">
            <main class="p-5">
                <h2 class="text-3xl section-title text-center">My Tournaments</h2>
                <div class="mb-8">
                    <h3 class="text-2xl text-cyan-300 font-orbitron mb-4">Created by Me</h3>
                    <div id="createdTournamentsList" class="space-y-4">
                        <p class="text-gray-400">Loading your created tournaments...</p>
                    </div>
                </div>
                <div>
                    <h3 class="text-2xl text-cyan-300 font-orbitron mb-4">Joined Tournaments</h3>
                    <div id="joinedTournamentsList" class="space-y-4">
                         <p class="text-gray-400">Loading tournaments you've joined...</p>
                    </div>
                </div>
            </main>
        </section>

         <section id="notifications" class="page-section">
            <main class="p-5">
                <h2 class="text-3xl section-title text-center">Notifications</h2>
                <div class="max-w-2xl mx-auto">
                    <div id="notification-list-container">
                        <div class="text-center text-gray-400 py-8">
                            <div class="loading-spinner mx-auto mb-4"></div>
                            <p>Loading notifications...</p>
                        </div>
                    </div>
                </div>
            </main>
        </section>

         <section id="settings" class="page-section">
            <main class="p-5">
                <h2 class="text-3xl section-title text-center">Settings</h2>
                <div class="tournament-card-cyber p-6 rounded-lg text-center">
                    <p class="text-xl text-gray-400">App settings and profile management.</p>
                    <p class="mt-2 text-sm text-gray-500">(Feature coming soon!)</p>
                </div>
            </main>
        </section>

    </div>
    <nav class="fixed bottom-0 left-0 right-0 bottom-nav-cyber flex justify-around items-center py-2.5 px-1 text-gray-400 shadow-2xl z-50">
        <a href="#" data-navigate="home" class="nav-item text-center active flex flex-col items-center justify-center p-1 rounded-md w-1/5 h-full">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-7 h-7 mx-auto"><path stroke-linecap="round" stroke-linejoin="round" d="m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h7.5" /></svg>
            <span class="text-xs mt-1">Home</span>
        </a>
        <a href="#" data-navigate="my-tournaments" class="nav-item text-center flex flex-col items-center justify-center p-1 rounded-md w-1/5 h-full">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-7 h-7 mx-auto"><path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-4.5A3.375 3.375 0 0 0 12.75 9.75H11.25A3.375 3.375 0 0 0 7.5 13.125V18.75m9 0h1.5a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15A2.25 2.25 0 0 0 2.25 6.75v10.5A2.25 2.25 0 0 0 4.5 19.5H6" /></svg>
            <span class="text-xs mt-1">My Tourneys</span>
        </a>
        <div class="create-btn-wrapper w-1/5 h-full flex justify-center items-center">
            <a href="#" data-navigate="create-tournament" class="nav-item text-center text-white">
                <div class="create-btn-glow rounded-full shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="3" stroke="currentColor" class="w-8 h-8"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>
                </div>
            </a>
        </div>
        <a href="#" data-navigate="notifications" class="nav-item text-center flex flex-col items-center justify-center p-1 rounded-md w-1/5 h-full">
            <div class="relative">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-7 h-7 mx-auto">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0" />
                </svg>
                <div id="notification-badge-container"></div>
            </div>
            <span class="text-xs mt-1">Notifications</span>
        </a>
        <a href="#" data-navigate="settings" class="nav-item text-center flex flex-col items-center justify-center p-1 rounded-md w-1/5 h-full">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-7 h-7 mx-auto"><path stroke-linecap="round" stroke-linejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75" /></svg>
            <span class="text-xs mt-1">Settings</span>
        </a>
    </nav>

    <div id="messageModal" class="modal-backdrop">
        <div class="modal-content">
            <p id="modalMessageText" class="mb-4"></p>
            <button id="closeModalBtn" class="btn btn-cyber-secondary px-6 py-2.5 text-sm">Close</button>
        </div>
    </div>

    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <!-- Import the functions you need from the SDKs you need -->
    <script type="module">
    import { initializeApp } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-app.js";
    import { connectRoninWallet, getRoninProvider } from './ronin-wallet.js';
    import { getStorage } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-storage.js";
    import { getAuth, signInAnonymously } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-auth.js";
    import { getFunctions } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-functions.js";
    import {
        getFirestore,
        collection,
        addDoc,
        getDoc,
        getDocs,
        query,
        where,
        orderBy,
        limit,
        startAt,
        endAt,
        FieldValue,
        Timestamp,
        arrayUnion,
        increment,
        runTransaction,
        doc,
        updateDoc,
        deleteDoc,
        onSnapshot
    } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore.js";

    // Add httpsCallable import
    import { httpsCallable } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-functions.js";

    // --- Firebase Configuration ---
    const firebaseConfig = {
        apiKey: "AIzaSyDEeRP56u6HHb6_PE1bIg694QN1MVUh9cg",
        authDomain: "cyberfield-2e321.firebaseapp.com",
        projectId: "cyberfield-2e321",
        storageBucket: "cyberfield-2e321.appspot.com",
        messagingSenderId: "943261356256",
        appId: "1:943261356256:web:675961d80dd96f051d4085",
        measurementId: "G-ESX1E2F7PQ"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const storage = getStorage(app);
    const auth = getAuth(app);
    const db = getFirestore(app);
    const functions = getFunctions(app);

    // Make Firebase instances available globally for React components
    window.app = app;
    window.storage = storage;
    window.auth = auth;
    window.db = db;
    window.functions = functions;

    // Notification system variables
    let notificationBadgeComponent = null;
    let notificationListComponent = null;

    // Tournament real-time listener
    let tournamentListener = null;

    // Add validation function
    function validateTournamentData(data) {
        const requiredFields = ['tournamentName', 'gameName', 'creatorAddress', 'createdAt', 'status', 'participants', 'participantCount'];
        const missingFields = requiredFields.filter(field => !(field in data));
        if (missingFields.length > 0) {
            throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }
        if (data.status !== 'upcoming') {
            throw new Error('Tournament status must be "upcoming"');
        }
        if (!Array.isArray(data.participants)) {
            throw new Error('Participants must be an array');
        }
        if (data.participantCount !== 0) {
            throw new Error('Participant count must start at 0');
        }
    }

    // --- Global State ---
    let currentUserAddress = null; // Stores Ronin wallet address after connection
    let currentTournamentData = null; // Stores data for the details page being viewed (includes Firestore ID)
    let currentTournamentId = null; // Stores Firestore ID of the tournament being viewed

    // --- DOM Elements ---
    const pageContainer = document.getElementById('pageContainer');
    const connectWalletBtn = document.getElementById('connectWalletBtn');
    const walletInfoDiv = document.getElementById('walletInfo');
    const walletAddressSpan = document.getElementById('walletAddress');
    const messageModal = document.getElementById('messageModal');
    const modalMessageText = document.getElementById('modalMessageText');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const navItems = document.querySelectorAll('.nav-item');
    const createTournamentForm = document.getElementById('createTournamentForm');
    const searchInput = document.getElementById('searchInput');

    // --- Wallet Connection Logic (Ronin) ---
    async function connectWallet() {
        try {
            const provider = getRoninProvider();
            if (!provider) {
                throw new Error('No Ronin wallet provider found');
            }
            const accounts = await provider.request({ method: 'eth_requestAccounts' });
            if (accounts && accounts.length > 0) {
                currentUserAddress = accounts[0];
                window.currentUserAddress = accounts[0]; // Store in window object for global access

                // Add anonymous authentication
                try {
                    // Ensure auth is properly initialized
                    if (!window.auth) {
                        throw new Error('Firebase Auth not initialized');
                    }

                    const userCredential = await signInAnonymously(window.auth);
                    console.log('Anonymous authentication successful', userCredential.user.uid);
                    // Store the auth token for later use
                    window.currentUser = userCredential.user;

                    // Update UI and show success message
                    updateWalletUI();
                    showModalMessage(`Wallet connected: ${truncateAddress(currentUserAddress)}`);

                    // Initialize notification system
                    initializeNotificationSystem();

                    // After connecting wallet, refresh content that might depend on user state
                    if (document.getElementById('my-tournaments').classList.contains('active')) {
                        loadMyTournaments();
                    }
                    if (document.getElementById('tournament-details').classList.contains('active') && currentTournamentId) {
                        loadTournamentDetails(currentTournamentId);
                    }
                } catch (authError) {
                    console.error('Anonymous auth error:', authError);
                    showModalMessage("Authentication failed. Please try again.");
                    return; // Don't proceed if auth fails
                }
            }
        } catch (error) {
            handleWalletError(error);
        }
    }

    function updateWalletUI() {
        if (currentUserAddress) {
            connectWalletBtn.classList.add('hidden');
            walletInfoDiv.classList.remove('hidden');
            walletAddressSpan.textContent = truncateAddress(currentUserAddress);
        } else {
            connectWalletBtn.classList.remove('hidden');
            walletInfoDiv.classList.add('hidden');
            walletAddressSpan.textContent = '';
        }
    }

    function truncateAddress(address) {
        if (!address) return '';
        return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
    }

    // --- Notification System Functions ---

    // Core notification functions using Firebase directly
    async function createNotification(userId, type, title, message, data = {}) {
        try {
            // Ensure Firebase is initialized
            if (!db || !addDoc || !collection || !Timestamp) {
                console.error('Firebase not properly initialized for notifications');
                return null;
            }

            const notification = {
                userId: userId.toLowerCase(),
                type,
                title,
                message,
                data,
                isRead: false,
                createdAt: Timestamp.now(),
                expiresAt: Timestamp.fromDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
            };

            const docRef = await addDoc(collection(db, 'notifications'), notification);
            console.log('Notification created:', docRef.id);
            return docRef.id;
        } catch (error) {
            console.error('Error creating notification:', error);
            throw error;
        }
    }

    async function getUserNotifications(userId, limitCount = 50, unreadOnly = false) {
        try {
            // Ensure Firebase is initialized
            if (!db || !collection || !query || !where || !orderBy || !limit) {
                console.error('Firebase not properly initialized for notifications');
                return [];
            }

            let q = query(
                collection(db, 'notifications'),
                where('userId', '==', userId.toLowerCase()),
                orderBy('createdAt', 'desc'),
                limit(limitCount)
            );

            if (unreadOnly) {
                q = query(
                    collection(db, 'notifications'),
                    where('userId', '==', userId.toLowerCase()),
                    where('isRead', '==', false),
                    orderBy('createdAt', 'desc'),
                    limit(limitCount)
                );
            }

            const snapshot = await getDocs(q);
            const notifications = [];

            snapshot.forEach(doc => {
                notifications.push({
                    id: doc.id,
                    ...doc.data(),
                    createdAt: doc.data().createdAt?.toDate() || new Date()
                });
            });

            return notifications;
        } catch (error) {
            console.error('Error fetching notifications:', error);
            throw error;
        }
    }

    async function markNotificationAsRead(notificationId) {
        try {
            // Ensure Firebase is initialized
            if (!db || !doc || !updateDoc || !Timestamp) {
                console.error('Firebase not properly initialized for notifications');
                return;
            }

            const notificationRef = doc(db, 'notifications', notificationId);
            await updateDoc(notificationRef, {
                isRead: true,
                readAt: Timestamp.now()
            });
            console.log('Notification marked as read:', notificationId);
        } catch (error) {
            console.error('Error marking notification as read:', error);
            throw error;
        }
    }

    async function deleteReadNotifications(userId) {
        try {
            // Ensure Firebase is initialized
            if (!db || !collection || !query || !where || !getDocs || !deleteDoc || !doc) {
                console.error('Firebase not properly initialized for notifications');
                return { success: false, error: 'Firebase not initialized' };
            }

            // Query for read notifications for this user
            const q = query(
                collection(db, 'notifications'),
                where('userId', '==', userId.toLowerCase()),
                where('isRead', '==', true)
            );

            const querySnapshot = await getDocs(q);
            const deletePromises = [];

            querySnapshot.forEach((docSnapshot) => {
                const notificationRef = doc(db, 'notifications', docSnapshot.id);
                deletePromises.push(deleteDoc(notificationRef));
            });

            await Promise.all(deletePromises);

            console.log(`Deleted ${deletePromises.length} read notifications for user: ${userId}`);
            return { success: true, deletedCount: deletePromises.length };
        } catch (error) {
            console.error('Error deleting read notifications:', error);
            return { success: false, error: error.message };
        }
    }

    // Make notification functions available globally for components
    window.notificationService = {
        createNotification,
        getUserNotifications,
        markNotificationAsRead,
        deleteReadNotifications
    };

    function initializeNotificationSystem() {
        if (!window.currentUserAddress || !window.NotificationComponents) {
            return;
        }

        // Initialize notification badge
        const badgeContainer = document.getElementById('notification-badge-container');
        if (badgeContainer && window.NotificationComponents.NotificationBadge) {
            notificationBadgeComponent = ReactDOM.render(
                React.createElement(window.NotificationComponents.NotificationBadge, {
                    userId: window.currentUserAddress
                }),
                badgeContainer
            );
        }

        // Initialize notification list if on notifications page
        const listContainer = document.getElementById('notification-list-container');
        if (listContainer && window.NotificationComponents.NotificationList) {
            notificationListComponent = ReactDOM.render(
                React.createElement(window.NotificationComponents.NotificationList, {
                    userId: window.currentUserAddress,
                    onNotificationClick: handleNotificationClick
                }),
                listContainer
            );
        }
    }

    function handleNotificationClick(notification) {
        // Handle notification click - navigate to relevant page
        if (notification.data.tournamentId) {
            navigateTo('tournament-details', { tournamentId: notification.data.tournamentId });
        }
    }

    function updateNotificationComponents() {
        // Re-initialize notification components when user changes
        initializeNotificationSystem();
    }

    function handleWalletError(error) {
        console.error('Wallet Error:', error);
        if (error.code === 4001) {
            showModalMessage('Connection request denied.');
        } else if (error.code === -32002) {
            showModalMessage('Please check your Ronin wallet for the connection request.');
        } else {
            showModalMessage(`Error: ${error.message || 'Unknown wallet error.'}`);
        }
        currentUserAddress = null;
        window.currentUserAddress = null; // Clear window object as well
        updateWalletUI();
    }

    async function setupWalletListeners() {
        const provider = getRoninProvider();
        if (!provider) return;

        provider.on('accountsChanged', async (accounts) => {
            currentUserAddress = accounts.length > 0 ? accounts[0] : null;
            window.currentUserAddress = accounts.length > 0 ? accounts[0] : null; // Update window object

            if (currentUserAddress) {
                // Ensure Firebase authentication when account changes
                if (!window.currentUser && window.auth) {
                    try {
                        const userCredential = await signInAnonymously(window.auth);
                        console.log('Anonymous authentication successful on account change', userCredential.user.uid);
                        window.currentUser = userCredential.user;
                        // Wait for auth state to propagate
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } catch (authError) {
                        console.warn('Could not authenticate on account change:', authError.message);
                    }
                }
            } else {
                // Clear authentication when wallet disconnected
                window.currentUser = null;
                showModalMessage('Wallet disconnected or locked.');
            }

            updateWalletUI();
            // Refresh content if current page depends on user
            if (document.getElementById('my-tournaments').classList.contains('active')) {
                loadMyTournaments();
            }
            if (document.getElementById('tournament-details').classList.contains('active') && currentTournamentId) {
                loadTournamentDetails(currentTournamentId);
            }
        });

        provider.on('chainChanged', (chainId) => {
            // Ronin Mainnet: 2020 (0x7e4), Saigon Testnet: 2021 (0x7e5)
            if (chainId !== '0x7e4' && chainId !== '2020' && chainId !== '0x7e5' && chainId !== '2021') {
                showModalMessage(`Switched to unsupported network (ID: ${chainId}). Please use Ronin Mainnet or Saigon Testnet.`);
            }
        });
    }

    async function checkInitialWalletConnection() {
        const provider = getRoninProvider();
        if (!provider) return;

        try {
            const accounts = await provider.enable();
            if (accounts && accounts.length > 0) {
                currentUserAddress = accounts[0];
                window.currentUserAddress = accounts[0]; // Store in window object

                // Also authenticate with Firebase if not already authenticated
                if (!window.currentUser && window.auth) {
                    try {
                        const userCredential = await signInAnonymously(window.auth);
                        console.log('Anonymous authentication successful', userCredential.user.uid);
                        window.currentUser = userCredential.user;
                        // Wait for auth state to propagate
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } catch (authError) {
                        console.warn('Could not authenticate silently:', authError.message);
                    }
                }

                updateWalletUI();
            }
        } catch (error) {
            console.warn('Could not silently retrieve existing accounts:', error.message);
        }
    }

    // --- Navigation Logic ---
    function navigateTo(pageId, data) {
        // Clean up tournament listener when navigating away
        if (tournamentListener) {
            tournamentListener();
            tournamentListener = null;
        }

        document.querySelectorAll('.page-section').forEach(page => page.classList.remove('active'));
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.classList.add('active');
            window.scrollTo(0, 0); // Scroll to top

            // Update nav item active states
            navItems.forEach(item => {
                const itemPageId = item.dataset.navigate;
                const isCreateButtonWrapper = item.closest('.create-btn-wrapper');

                item.classList.remove('active'); // Remove active from all

                if (isCreateButtonWrapper && pageId === 'create-tournament') {
                     // The create button itself doesn't get 'active' class, its parent wrapper implies it
                     // Or, if you want the icon to glow, you'd target the inner SVG or a class on the <a>
                } else if (itemPageId === pageId) {
                    item.classList.add('active');
                }
            });
            // Special handling for the middle "Create" button if it should also visually indicate active state
            const createNavItem = document.querySelector('.create-btn-wrapper a[data-navigate="create-tournament"]');
            if (pageId === 'create-tournament' && createNavItem) {
                // Add a specific class to the create button or its icon if needed for active state
            }


            // Page-specific loading logic
            if (pageId === 'home') {
                loadFeaturedTournaments();
                loadUpcomingTournaments();
            } else if (pageId === 'tournament-details' && data && data.tournamentId) {
                currentTournamentId = data.tournamentId; // Store the ID
                loadTournamentDetails(data.tournamentId);
            } else if (pageId === 'create-tournament') {
                if (!currentUserAddress) {
                    showModalMessage("Please connect your wallet to create a tournament.");
                    navigateTo('home'); return;
                }
                if(createTournamentForm) createTournamentForm.reset();
            } else if (pageId === 'my-tournaments') {
                if (!currentUserAddress) {
                    showModalMessage("Please connect your wallet to see your tournaments.");
                    navigateTo('home'); return;
                }
                loadMyTournaments();
            } else if (pageId === 'notifications') {
                if (!currentUserAddress) {
                    showModalMessage("Please connect your wallet to see your notifications.");
                    navigateTo('home'); return;
                }
                // Initialize notification list component
                initializeNotificationSystem();
            }

        } else {
            console.error(`Page with ID "${pageId}" not found.`);
            navigateTo('home'); // Default to home
        }
    }

    // --- Modal Logic ---
    function showModalMessage(message, duration = 3000) {
        modalMessageText.textContent = message;
        messageModal.classList.add('active');
        if (duration > 0) {
            setTimeout(() => {
                if (modalMessageText.textContent === message) { // Only close if message hasn't changed
                     messageModal.classList.remove('active');
                }
            }, duration);
        }
    }
    closeModalBtn.addEventListener('click', () => messageModal.classList.remove('active'));
    messageModal.addEventListener('click', (event) => {
        if (event.target === messageModal) messageModal.classList.remove('active');
    });

    // --- Tournament Logic (Firebase Integrated) ---
    async function handleCreateTournament(event) {
        event.preventDefault();
        const submitButton = document.getElementById('submitCreateTournamentBtn');
        submitButton.disabled = true;
        submitButton.textContent = 'Creating...';

        if (!currentUserAddress) {
            showModalMessage("Please connect your wallet first.");
            submitButton.disabled = false;
            submitButton.textContent = 'Create Tournament';
            return;
        }

        try {
            const formData = new FormData(event.target);
            const tournamentData = Object.fromEntries(formData.entries());

            // Add required fields
            tournamentData.creatorAddress = currentUserAddress;
            tournamentData.createdAt = Timestamp.now();
            tournamentData.status = "upcoming";
            tournamentData.participants = [];
            tournamentData.participantCount = 0;

            // Convert startDate to Firestore Timestamp
            if (!tournamentData.startDate) {
                throw new Error("Start date is required.");
            }
            tournamentData.startDate = Timestamp.fromDate(new Date(tournamentData.startDate));

            if (!tournamentData.tournamentName || !tournamentData.gameName) {
                throw new Error("Tournament Name and Game are required.");
            }

            // For team tournaments, add team formation info
            if (tournamentData.tournamentFormat === '2v2' || tournamentData.tournamentFormat === '3v3') {
                tournamentData.isTeamTournament = true;
                tournamentData.teamSize = tournamentData.tournamentFormat === '2v2' ? 2 : 3;
            }

            // Parse and validate entry fee
            const feeParts = parseEntryFee(tournamentData.entryFee);
            tournamentData.entryFeeAmount = feeParts.amount;
            tournamentData.entryFeeToken = feeParts.token;
            tournamentData.maxEntries = parseInt(tournamentData.maxEntries) || 0;
            tournamentData.bannerImageUrl = tournamentData.bannerImageUrl || '';

            // Validate tournament data against Firestore rules
            validateTournamentData(tournamentData);

            // Create tournament
            const docRef = await addDoc(collection(db, 'tournaments'), tournamentData);
            showModalMessage(`Tournament "${tournamentData.tournamentName}" created successfully!`);
            event.target.reset();
            navigateTo('tournament-details', { tournamentId: docRef.id });
        } catch (error) {
            console.error("Error creating tournament: ", error);
            if (error.code === 'permission-denied') {
                showModalMessage("You don't have permission to create tournaments. Please make sure you're connected.");
            } else {
                showModalMessage(`Error creating tournament: ${error.message}`);
            }
        } finally {
            submitButton.disabled = false;
            submitButton.textContent = 'Create Tournament';
        }
    }

    // --- Team Formation Logic ---
    let teamCounter = 0;

    function toggleTeamFormationSection() {
        const formatSelect = document.getElementById('tournamentFormat');
        const teamInfo = document.getElementById('teamTournamentInfo');

        if (formatSelect.value === '2v2' || formatSelect.value === '3v3') {
            teamInfo.style.display = 'block';
        } else {
            teamInfo.style.display = 'none';
        }
    }

    // --- Team Registration Modal Logic ---
    function showTeamFormationModal(teamSize) {
        const modal = document.getElementById('teamFormationModal');
        const teammateInputs = document.getElementById('teammateInputs');
        const teamFormInterface = document.getElementById('teamFormInterface');

        if (!modal) {
            console.error('Team formation modal not found!');
            return;
        }

        // Clear previous inputs
        teammateInputs.innerHTML = '';

        // Add team size information
        const teamSizeInfo = document.createElement('div');
        teamSizeInfo.className = 'form-group';
        teamSizeInfo.innerHTML = `
            <div class="alert alert-info text-sm mb-4">
                <strong>Team Requirements:</strong> You need exactly ${teamSize - 1} teammate${teamSize > 2 ? 's' : ''} to form a ${teamSize === 2 ? '2v2' : '3v3'} team.
                <br><strong>Important:</strong> Each team member must have a unique wallet address. You cannot use your own address as a teammate.
            </div>
        `;
        teammateInputs.appendChild(teamSizeInfo);

        // Create teammate input fields
        for (let i = 1; i < teamSize; i++) { // -1 because current user is already included
            const inputDiv = document.createElement('div');
            inputDiv.className = 'form-group';
            inputDiv.innerHTML = `
                <label class="form-label text-sm">Teammate ${i} Wallet Address</label>
                <input type="text" class="input-cyber teammate-input" placeholder="0x..." data-teammate-index="${i}">
                <small class="text-muted">Enter a unique wallet address (different from yours and other teammates)</small>
            `;
            teammateInputs.appendChild(inputDiv);
        }

        // Show/hide team form based on join type
        toggleTeamFormInterface();

        modal.classList.add('active');
    }

    function hideTeamFormationModal() {
        const modal = document.getElementById('teamFormationModal');
        const teamNameInput = document.getElementById('teamNameInput');
        const teammateInputs = document.querySelectorAll('.teammate-input');
        const joinWithTeamRadio = document.getElementById('joinWithTeam');

        // Reset form
        teamNameInput.value = '';
        teammateInputs.forEach(input => input.value = '');
        joinWithTeamRadio.checked = true;
        toggleTeamFormInterface();

        modal.classList.remove('active');
    }

    function toggleTeamFormInterface() {
        const joinWithTeamRadio = document.getElementById('joinWithTeam');
        const teamFormInterface = document.getElementById('teamFormInterface');

        if (joinWithTeamRadio.checked) {
            teamFormInterface.style.display = 'block';
        } else {
            teamFormInterface.style.display = 'none';
        }
    }

    function getTeamFormationData() {
        const joinType = document.querySelector('input[name="joinType"]:checked').value;

        if (joinType === 'solo') {
            return { joinType: 'solo' };
        }

        // Get team data
        const teamName = document.getElementById('teamNameInput').value.trim();
        const teammateInputs = document.querySelectorAll('.teammate-input');
        const teammates = [];
        const seenAddresses = new Set();

        // Get current user's wallet address for self-inclusion check
        const currentUserAddress = currentUserWalletAddress ? currentUserWalletAddress.toLowerCase() : null;

        teammateInputs.forEach((input, index) => {
            const address = input.value.trim();
            if (address) {
                // Validate wallet address format
                if (address.length !== 42 || !address.startsWith('0x')) {
                    throw new Error(`Invalid wallet address format for teammate ${index + 1}: ${address}`);
                }

                const normalizedAddress = address.toLowerCase();

                // Check if user is trying to add their own wallet address
                if (currentUserAddress && normalizedAddress === currentUserAddress) {
                    throw new Error(`You cannot add your own wallet address (${address}) as a teammate. You are already the team captain.`);
                }

                // Check for duplicate addresses within the team
                if (seenAddresses.has(normalizedAddress)) {
                    throw new Error(`Duplicate wallet address detected: ${address}. Each teammate must have a unique wallet address.`);
                }

                seenAddresses.add(normalizedAddress);
                teammates.push(normalizedAddress);
            }
        });

        // Validate team size requirements based on tournament format
        const formatSelect = document.getElementById('tournamentFormat');
        if (formatSelect) {
            const tournamentFormat = formatSelect.value;
            const requiredTeammateCount = tournamentFormat === '2v2' ? 1 : tournamentFormat === '3v3' ? 2 : 0;

            if (requiredTeammateCount > 0 && teammates.length !== requiredTeammateCount) {
                throw new Error(`${tournamentFormat} tournaments require exactly ${requiredTeammateCount} teammate${requiredTeammateCount > 1 ? 's' : ''}. You have entered ${teammates.length}.`);
            }
        }

        return {
            joinType: 'team',
            teamName: teamName || null,
            teammates: teammates
        };
    }



    function createTournamentCard(tournament, containerId) {
        const card = document.createElement('div');
        card.className = 'tournament-card-cyber rounded-lg p-5 shadow-lg cursor-pointer';
        card.dataset.navigate = 'tournament-details';
        card.dataset.tournamentId = tournament.id;

        // Get the correct game icon based on game name
        let gameIconUrl = tournament.bannerImageUrl;
        if (!gameIconUrl) {
            switch(tournament.gameName) {
                case 'Axie Infinity: Origins':
                    gameIconUrl = '/public/axieInfinityOrigins.png';
                    break;
                case 'Axie Infinity: Classic':
                    gameIconUrl = '/public/axieInfinityClassic.png';
                    break;
                case 'Moshi Admirals':
                    gameIconUrl = '/public/moshiAdmirals.png';
                    break;
                default:
                    gameIconUrl = '/public/cyberfieldLogo.png';
            }
        }

        const entryFeeDisplay = tournament.entryFeeAmount == 0 ? 'FREE' : `${tournament.entryFeeAmount} ${tournament.entryFeeToken}`;
        const startDateDisplay = tournament.startDate ? tournament.startDate.toDate().toLocaleDateString() : 'TBA';

        // Determine status display and color
        let statusText = tournament.status.toUpperCase();
        let statusColor = 'tag-cyber';
        let countdownHtml = '';

        if (tournament.status === 'registrationClosed' && tournament.registrationClosedAt) {
            statusColor = 'tag-cyber-yellow';
            const registrationClosedTime = tournament.registrationClosedAt.toDate();
            const startTime = new Date(registrationClosedTime.getTime() + (30 * 60 * 1000));
            const now = new Date();
            const timeLeft = startTime - now;

            if (timeLeft > 0) {
                const minutes = Math.floor(timeLeft / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                countdownHtml = `
                    <div class="mt-2 text-sm text-cyan-300">
                        Starts in: ${minutes}m ${seconds}s
                    </div>
                `;
            }
        } else if (tournament.status === 'live') {
            statusColor = 'tag-cyber-red';
        } else if (tournament.status === 'upcoming') {
            statusColor = 'tag-cyber-green';
        }

        card.innerHTML = `
            <div class="flex items-center space-x-5">
                <img src="${gameIconUrl}" alt="Game Icon" class="w-20 h-20 rounded-lg object-cover border-2 border-cyan-700/50">
                <div class="flex-1">
                    <h3 class="font-semibold text-xl text-white font-orbitron">${tournament.tournamentName}</h3>
                    <p class="text-sm text-purple-300 mt-0.5">${tournament.gameName}</p>
                    <div class="mt-2.5 flex flex-wrap gap-x-3 gap-y-2 text-xs">
                        <span class="${statusColor}">${statusText}</span>
                        <span class="tag-cyber">Entry: ${entryFeeDisplay}</span>
                        <span class="tag-cyber">Prize: ${tournament.prizePool.substring(0,20)}...</span>
                        <span class="tag-cyber">Starts: ${startDateDisplay}</span>
                    </div>
                    ${countdownHtml}
                </div>
            </div>
            <div class="mt-5 flex justify-between items-center">
                <p class="text-sm text-gray-400">${tournament.participantCount || 0} / ${tournament.maxEntries == 0 ? 'Unlimited' : tournament.maxEntries} Participants</p>
                <button class="btn btn-cyber-secondary px-5 py-2 text-sm">View Details</button>
            </div>
        `;
        return card;
    }

    async function loadFeaturedTournaments() {
        const container = document.getElementById('featuredTournamentsContainer');
        const loadingP = document.getElementById('featuredLoading');
        if (!container || !loadingP) return;
        container.innerHTML = ''; // Clear previous
        loadingP.style.display = 'block';

        try {
            const tournamentsRef = collection(db, 'tournaments');
            const q = query(
                tournamentsRef,
                where('status', '==', 'upcoming'),
                orderBy('createdAt', 'desc'),
                limit(1)
            );
            const querySnapshot = await getDocs(q);

            if (querySnapshot.empty) {
                loadingP.textContent = 'No featured tournaments found.';
                return;
            }
            querySnapshot.forEach(doc => {
                const tournament = { id: doc.id, ...doc.data() };
                // Using a more prominent featured card style
                const card = document.createElement('div');
                card.className = 'relative rounded-lg overflow-hidden shadow-2xl tournament-card-cyber cursor-pointer';
                card.dataset.navigate = 'tournament-details';
                card.dataset.tournamentId = tournament.id;
                const banner = tournament.bannerImageUrl || `https://placehold.co/600x300/0A0A1A/60EFFF?text=${tournament.tournamentName.toUpperCase()}`;
                const prize = tournament.prizePool || 'Not specified';
                const startDate = tournament.startDate ? tournament.startDate.toDate().toLocaleDateString() : 'TBA';

                card.innerHTML = `
                    <img src="${banner}" alt="${tournament.tournamentName} Banner" class="w-full h-56 object-cover opacity-80 card-banner">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/95 via-black/60 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-6">
                        <h3 class="text-3xl font-bold text-white font-orbitron">${tournament.tournamentName}</h3>
                        <p class="text-md text-cyan-300 mt-1.5">Prize Pool: ${prize} | Starts: ${startDate}</p>
                        <button class="mt-4 btn btn-cyber-primary px-6 py-3 text-base">Enter the Arena</button>
                    </div>
                `;
                container.appendChild(card);
            });
            loadingP.style.display = 'none';
        } catch (error) {
            console.error("Error loading featured tournaments: ", error);
            loadingP.textContent = 'Error loading tournaments.';
        }
    }

    async function loadUpcomingTournaments(filter = {}) {
        const container = document.getElementById('upcomingTournamentsContainer');
        const loadingP = document.getElementById('upcomingLoading');
        if (!container || !loadingP) return;

        container.innerHTML = ''; // Clear previous
        loadingP.style.display = 'block';
        loadingP.textContent = 'Loading upcoming tournaments...';

        try {
            const tournamentsRef = collection(db, 'tournaments');
            let q;

            if (filter.gameName) {
                q = query(
                    tournamentsRef,
                    where('status', 'in', ['upcoming', 'live']),
                    where('gameName', '==', filter.gameName),
                    orderBy('startDate', 'asc')
                );
            } else if (filter.searchTerm) {
                q = query(
                    tournamentsRef,
                    where('status', 'in', ['upcoming', 'live']),
                    orderBy('tournamentName'),
                    startAt(filter.searchTerm),
                    endAt(filter.searchTerm + '\uf8ff')
                );
            } else {
                q = query(
                    tournamentsRef,
                    where('status', 'in', ['upcoming', 'live']),
                    orderBy('startDate', 'asc')
                );
            }

            const querySnapshot = await getDocs(q);

            if (querySnapshot.empty) {
                loadingP.textContent = 'No upcoming tournaments found matching your criteria.';
                return;
            }
            querySnapshot.forEach(doc => {
                const tournament = { id: doc.id, ...doc.data() };
                container.appendChild(createTournamentCard(tournament, 'upcomingTournamentsContainer'));
            });
            loadingP.style.display = 'none';
        } catch (error) {
            console.error("Error loading upcoming tournaments: ", error);
            loadingP.textContent = 'Error loading tournaments.';
        }
    }


    async function loadTournamentDetails(tournamentId) {
        console.log(`Loading details for tournament ID: ${tournamentId}`);
        const contentEl = document.getElementById('tournamentDetailsContent');
        contentEl.classList.add('opacity-50'); // Visual feedback for loading

        // Clean up any existing listener
        if (tournamentListener) {
            tournamentListener();
            tournamentListener = null;
        }

        try {
            const docRef = doc(db, 'tournaments', tournamentId);
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                currentTournamentData = { id: docSnap.id, ...docSnap.data() };
                renderTournamentDetails(currentTournamentData);

                // Set up real-time listener for bracket updates
                tournamentListener = onSnapshot(docRef, (doc) => {
                    if (doc.exists()) {
                        const updatedData = { id: doc.id, ...doc.data() };
                        currentTournamentData = updatedData;
                        renderTournamentDetails(updatedData);
                    }
                }, (error) => {
                    console.error("Error in tournament listener:", error);
                });
            } else {
                showModalMessage(`Tournament ${tournamentId} not found.`);
                navigateTo('home');
            }
        } catch (error) {
            console.error("Error loading tournament details:", error);
            showModalMessage("Error loading tournament details.");
            navigateTo('home');
        } finally {
            contentEl.classList.remove('opacity-50');
        }
    }

    function renderTournamentDetails(data) {
        // Determine if this is a team tournament first
        const isTeamTournament = data.tournamentFormat === '2v2' || data.tournamentFormat === '3v3';

        document.getElementById('detailsTournamentName').textContent = data.tournamentName || 'N/A';
        document.getElementById('detailsGameName').textContent = data.gameName || 'N/A';

        // Display tournament format with bracket format for team tournaments
        let formatDisplay = data.tournamentFormat || 'N/A';
        if (isTeamTournament && data.bracketFormat) {
            formatDisplay = `${data.tournamentFormat} (${data.bracketFormat})`;
        }
        document.getElementById('detailsTournamentFormat').textContent = formatDisplay;
        document.getElementById('detailsMatchFormat').textContent = data.matchFormat || 'N/A';
        document.getElementById('detailsStartDate').textContent = data.startDate ? data.startDate.toDate().toLocaleString() : 'N/A';

        const statusEl = document.getElementById('detailsStatus');
        const countdownTimer = document.getElementById('countdownTimer');
        const countdownDisplay = document.getElementById('countdownDisplay');

        // Clear any existing countdown interval
        if (window.countdownInterval) {
            clearInterval(window.countdownInterval);
        }

        if (data.status === 'registrationClosed' && data.registrationClosedAt) {
            const registrationClosedTime = data.registrationClosedAt.toDate();
            const startTime = new Date(registrationClosedTime.getTime() + (30 * 60 * 1000)); // 30 minutes after registration closed

            statusEl.textContent = 'REGISTRATION CLOSED';
            statusEl.className = 'tag-cyber tag-cyber-yellow';

            // Show countdown timer
            countdownTimer.style.display = 'block';

            // Update countdown every second
            window.countdownInterval = setInterval(() => {
                const now = new Date();
                const timeLeft = startTime - now;

                if (timeLeft <= 0) {
                    clearInterval(window.countdownInterval);
                    countdownTimer.style.display = 'none';
                    return;
                }

                const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                countdownDisplay.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        } else {
            countdownTimer.style.display = 'none';
            statusEl.textContent = data.status ? data.status.toUpperCase() : 'N/A';
            statusEl.className = `tag-cyber ${data.status === 'live' ? 'tag-cyber-red' : (data.status === 'upcoming' ? 'tag-cyber-green' : 'tag-cyber')}`;
        }

        document.getElementById('detailsEntryFee').textContent = data.entryFeeAmount == 0 ? 'FREE' : `${data.entryFeeAmount} ${data.entryFeeToken}`;
        document.getElementById('detailsCreatorAddress').textContent = data.creatorAddress ? truncateAddress(data.creatorAddress) : 'N/A';
        document.getElementById('detailsPrizePool').textContent = data.prizePool || 'N/A';

        // Calculate participant count based on tournament type
        let participantCount = 0;

        if (isTeamTournament) {
            // For team tournaments, count teams (not individual players)
            participantCount = data.teamParticipants ? data.teamParticipants.length : 0;
        } else {
            // For individual tournaments, count individual participants
            participantCount = data.participants ? data.participants.length : 0;
        }

        document.getElementById('detailsParticipants').textContent = participantCount;
        document.getElementById('participantsCountDisplay').textContent = participantCount;
        document.getElementById('detailsMaxEntries').textContent = data.maxEntries == 0 ? 'Unlimited' : (data.maxEntries || 'N/A');
        document.getElementById('detailsRules').textContent = data.rules || 'No rules provided.';

        const bannerImg = document.getElementById('detailsBannerImage');
        if (data.bannerImageUrl) {
            bannerImg.src = data.bannerImageUrl;
            bannerImg.alt = `${data.tournamentName} Banner`;
            bannerImg.style.display = 'block';
        } else {
            bannerImg.style.display = 'none';
        }

        const streamSection = document.getElementById('detailsStreamSection');
        const streamEmbed = document.getElementById('detailsStreamEmbed');
        if (data.streamUrl) {
            let embedHtml = '';
            if (data.streamUrl.includes('twitch.tv')) {
                const channel = data.streamUrl.substring(data.streamUrl.lastIndexOf('/') + 1).split('?')[0];
                embedHtml = `<iframe src="https://player.twitch.tv/?channel=${channel}&parent=${window.location.hostname}&autoplay=false&muted=true" frameborder="0" allowfullscreen="true" scrolling="no" class="w-full h-full"></iframe>`;
            } else if (data.streamUrl.includes('youtube.com/watch?v=')) {
                const videoId = data.streamUrl.split('v=')[1].split('&')[0];
                embedHtml = `<iframe src="https://www.youtube.com/embed/${videoId}?autoplay=0" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen class="w-full h-full"></iframe>`;
            } else if (data.streamUrl.includes('youtu.be/')) {
                const videoId = data.streamUrl.split('youtu.be/')[1].split('?')[0];
                embedHtml = `<iframe src="https://www.youtube.com/embed/${videoId}?autoplay=0" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen class="w-full h-full"></iframe>`;
            } else if (data.streamUrl.includes('discord.com/channels/')) {
                // Extract server ID and channel ID from Discord URL
                const urlParts = data.streamUrl.split('/');
                const serverId = urlParts[urlParts.indexOf('channels') + 1];
                const channelId = urlParts[urlParts.indexOf('channels') + 2];
                embedHtml = `
                    <div class="discord-embed w-full h-full flex items-center justify-center bg-[#36393f] rounded-lg">
                        <div class="text-center p-6">
                            <svg class="w-16 h-16 mx-auto mb-4 text-[#5865F2]" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-************ 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994.021-.041.001-.09-.041-.106a13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                            </svg>
                            <h3 class="text-xl font-bold text-white mb-2">Join the Discord Stream</h3>
                            <p class="text-gray-400 mb-4">Click below to join the tournament's Discord channel</p>
                            <a href="${data.streamUrl}" target="_blank" rel="noopener noreferrer" class="btn btn-cyber-primary px-6 py-3 text-base">
                                <svg class="w-5 h-5 inline-block mr-2" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-************ 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028c.462-.63.874-1.295 1.226-1.994.021-.041.001-.09-.041-.106a13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                                </svg>
                                Join Discord
                            </a>
                        </div>
                    </div>
                `;
            }

            if(embedHtml) { streamEmbed.innerHTML = embedHtml; streamSection.style.display = 'block'; }
            else { streamEmbed.innerHTML = '<p class="text-center text-gray-400">Unsupported stream URL.</p>'; streamSection.style.display = 'block';}
        } else { streamSection.style.display = 'none'; }

        const participantsListEl = document.getElementById('participantsList');
        participantsListEl.innerHTML = ''; // Clear previous

        if(participantCount > 0) {
            if (isTeamTournament && data.teamParticipants) {
                // Display team information for team tournaments
                data.teamParticipants.slice(0, 20).forEach(team => { // Show first 20 teams
                    const teamContainer = document.createElement('div');
                    teamContainer.className = 'mb-2 p-2 border border-cyan-700/30 rounded bg-cyan-900/10';

                    const teamNameEl = document.createElement('div');
                    teamNameEl.className = 'text-cyan-300 font-semibold text-sm mb-1';
                    teamNameEl.textContent = team.name || 'Unnamed Team';
                    teamContainer.appendChild(teamNameEl);

                    const membersEl = document.createElement('div');
                    membersEl.className = 'flex flex-wrap gap-1';

                    if (team.members && team.members.length > 0) {
                        team.members.forEach(memberAddr => {
                            const memberTag = document.createElement('span');
                            memberTag.className = 'tag-cyber text-xs';
                            memberTag.textContent = truncateAddress(memberAddr);
                            membersEl.appendChild(memberTag);
                        });
                    } else {
                        const noMembersTag = document.createElement('span');
                        noMembersTag.className = 'text-xs text-gray-400';
                        noMembersTag.textContent = 'No members listed';
                        membersEl.appendChild(noMembersTag);
                    }

                    teamContainer.appendChild(membersEl);
                    participantsListEl.appendChild(teamContainer);
                });

                if(participantCount > 20) {
                    participantsListEl.innerHTML += `<p class="w-full text-xs text-gray-400 mt-2">...and ${participantCount - 20} more teams.</p>`;
                }
            } else if (data.participants) {
                // Display individual participants for individual tournaments
                data.participants.slice(0, 20).forEach(pAddr => { // Show first 20
                    const pTag = document.createElement('span');
                    pTag.className = 'tag-cyber text-xs';
                    pTag.textContent = truncateAddress(pAddr);
                    participantsListEl.appendChild(pTag);
                });

                if(participantCount > 20) {
                    participantsListEl.innerHTML += `<p class="w-full text-xs text-gray-400 mt-2">...and ${participantCount - 20} more.</p>`;
                }
            }
        } else {
            const noParticipantsText = isTeamTournament ? 'No teams yet.' : 'No participants yet.';
            participantsListEl.innerHTML = `<p class="w-full text-sm text-gray-400">${noParticipantsText}</p>`;
        }

        const joinBtn = document.getElementById('joinTournamentBtn');
        joinBtn.disabled = true; // Disable by default
        joinBtn.classList.remove('btn-cyber-primary', 'btn-cyber-secondary'); // Reset classes

        if (data.status === 'upcoming') {
            const currentUserWalletAddress = currentUserAddress?.toLowerCase();
            let userAlreadyJoined = false;

            if (currentUserWalletAddress) {
                if (isTeamTournament && data.teamParticipants) {
                    // Check if user is already in any team
                    userAlreadyJoined = data.teamParticipants.some(team =>
                        team.members && team.members.includes(currentUserWalletAddress)
                    );
                } else if (data.participants) {
                    // Check individual participants
                    userAlreadyJoined = data.participants.includes(currentUserWalletAddress);
                }
            }

            if (userAlreadyJoined) {
                joinBtn.textContent = isTeamTournament ? 'Already in Team' : 'Already Joined';
                joinBtn.classList.add('btn-cyber-secondary');
            } else if (data.maxEntries != 0 && participantCount >= data.maxEntries) {
                joinBtn.textContent = 'Tournament Full';
                joinBtn.classList.add('btn-cyber-secondary');
            } else {
                joinBtn.textContent = isTeamTournament ? 'Join as Team' : 'Join Tournament';
                joinBtn.disabled = false;
                joinBtn.classList.add('btn-cyber-primary');
            }
        } else if (data.status === 'live') {
            joinBtn.textContent = 'Tournament Live';
            joinBtn.classList.add('btn-cyber-secondary');
        } else { // ended, cancelled etc.
            joinBtn.textContent = 'Tournament Closed';
            joinBtn.classList.add('btn-cyber-secondary');
        }

        // Show/hide bracket section based on tournament status and start time
        const bracketSection = document.getElementById('bracketSection');
        const bracketContainer = document.getElementById('bracketContainer');

        if (data.status === 'live' || data.status === 'registrationClosed' || data.status === 'completed') {
            bracketSection.style.display = 'block';
            if (data.bracketData) {
                // Debug logging
                console.log('Rendering bracket for tournament:', data.id);
                console.log('Bracket data format:', data.bracketData.format);
                console.log('Bracket data structure:', {
                    format: data.bracketData.format,
                    hasRounds: !!data.bracketData.rounds,
                    roundsCount: data.bracketData.rounds?.length,
                    hasMatchesById: !!data.bracketData.matchesById,
                    matchesCount: Object.keys(data.bracketData.matchesById || {}).length,
                    hasParticipants: !!data.bracketData.participants,
                    participantsCount: data.bracketData.participants?.length
                });
                console.log('BracketView component available:', !!window.BracketView);

                // Initialize bracket view using React
                bracketContainer.innerHTML = ''; // Clear placeholder

                // Always create a new root to avoid conflicts between tournaments
                if (window.bracketRoot) {
                    try {
                        window.bracketRoot.unmount();
                    } catch (e) {
                        console.warn('Error unmounting previous bracket root:', e);
                    }
                }
                window.bracketRoot = ReactDOM.createRoot(bracketContainer);

                const bracketView = React.createElement(window.BracketView, {
                    tournamentId: data.id,
                    currentUserAddress: currentUserAddress,
                    bracketData: data.bracketData,
                    tournamentData: data
                });

                // Use new root to render
                try {
                    window.bracketRoot.render(React.createElement(React.StrictMode, null, bracketView));
                    console.log('✅ Bracket rendered successfully');
                } catch (error) {
                    console.error('❌ Error rendering bracket:', error);
                    bracketContainer.innerHTML = '<p class="text-red-400">Error displaying bracket. Check console for details.</p>';
                }
            } else {
                bracketContainer.innerHTML = '<p class="text-gray-400">Bracket is being generated...</p>';
            }
        } else {
            bracketSection.style.display = 'none';
        }

        // Show/hide admin control section based on whether current user is the tournament creator
        const adminControlSection = document.getElementById('adminControlSection');
        const adminControlContainer = document.getElementById('adminControlContainer');

        console.log('Admin control check:', {
            currentUserAddress,
            creatorAddress: data.creatorAddress,
            currentUserAddressLower: currentUserAddress?.toLowerCase(),
            creatorAddressLower: data.creatorAddress?.toLowerCase(),
            addressMatch: currentUserAddress && data.creatorAddress &&
                currentUserAddress.toLowerCase() === data.creatorAddress.toLowerCase(),
            adminControlPanelAvailable: !!window.AdminControlPanel,
            tournamentId: data.id,
            tournamentName: data.tournamentName
        });

        if (currentUserAddress && data.creatorAddress &&
            currentUserAddress.toLowerCase() === data.creatorAddress.toLowerCase()) {

            // Check if AdminControlPanel component is available
            if (!window.AdminControlPanel) {
                console.warn('AdminControlPanel component not available. Attempting to retry in 1 second...');
                adminControlSection.style.display = 'block';
                adminControlContainer.innerHTML = '<p class="text-yellow-400">Loading admin controls...</p>';

                // Retry after a short delay to allow bundles to load
                setTimeout(() => {
                    if (window.AdminControlPanel) {
                        console.log('AdminControlPanel now available, retrying render...');
                        renderTournamentDetails(data); // Re-render with admin controls
                    } else {
                        console.error('AdminControlPanel component still not available after retry.');
                        adminControlContainer.innerHTML = '<p class="text-red-400">Admin controls failed to load. Please refresh the page.</p>';
                    }
                }, 1000);
                return;
            }

            adminControlSection.style.display = 'block';

            // Show debug button for troubleshooting
            const debugBtn = document.getElementById('debugAdminBtn');
            if (debugBtn) debugBtn.style.display = 'inline-block';

            // Clear previous admin controls
            adminControlContainer.innerHTML = '';

            try {
                // Create root only if it doesn't exist
                if (!window.adminRoot) {
                    window.adminRoot = ReactDOM.createRoot(adminControlContainer);
                }

                const adminPanel = React.createElement(window.AdminControlPanel, {
                    tournament: data,
                    currentUserAddress: currentUserAddress
                });

                // Use existing root to render
                window.adminRoot.render(React.createElement(React.StrictMode, null, adminPanel));
                console.log('✅ Admin controls rendered successfully');
            } catch (error) {
                console.error('❌ Error rendering admin controls:', error);
                adminControlContainer.innerHTML = '<p class="text-red-400">Error loading admin controls. Check console for details.</p>';
            }
        } else {
            adminControlSection.style.display = 'none';

            // Hide debug button when admin controls are not shown
            const debugBtn = document.getElementById('debugAdminBtn');
            if (debugBtn) debugBtn.style.display = 'none';
        }
    }

    function parseEntryFee(feeString) {
        if (!feeString || feeString.toLowerCase() === 'free' || feeString.trim() === '0') {
            return { amount: 0, token: 'FREE', amountHex: '0x0' };
        }
        const parts = feeString.trim().split(/\s+/);
        const amount = parseFloat(parts[0]);
        const token = parts.length > 1 ? parts[1].toUpperCase() : 'RON';

        if (isNaN(amount) || amount < 0) {
            return { amount: 0, token: 'FREE', amountHex: '0x0' };
        }

        let amountHex = '0x0';
        if (token === 'RON' && amount > 0) {
            // IMPORTANT: For production, use a BigNumber library for wei conversion!
            // This is a simplified example and may have precision issues for large numbers.
            const amountInWei = BigInt(Math.round(amount * (10**18))); // Use BigInt for large numbers
            amountHex = '0x' + amountInWei.toString(16);
        }
        // For ERC20, amountHex is not directly used for 'value' in tx, but amount is used with token decimals.
        return { amount, token, amountHex };
    }

    async function handleJoinTournament() {
        const joinBtn = document.getElementById('joinTournamentBtn');
        joinBtn.disabled = true;

        if (!currentUserAddress) {
            showModalMessage("Please connect your wallet to join.");
            joinBtn.disabled = false;
            return;
        }

        // Check if this is a team tournament
        if (currentTournamentData && (currentTournamentData.tournamentFormat === '2v2' || currentTournamentData.tournamentFormat === '3v3')) {
            const teamSize = currentTournamentData.tournamentFormat === '2v2' ? 2 : 3;
            showTeamFormationModal(teamSize);
            joinBtn.disabled = false;
            return;
        }

        // For individual tournaments, proceed with normal join logic
        await processJoinTournament();
    }

    async function processJoinTournament(teamData = null) {
        const joinBtn = document.getElementById('joinTournamentBtn');
        joinBtn.disabled = true;

        // Check if user is authenticated with Firebase
        if (!window.currentUser) {
            // Try to authenticate now if not already done
            try {
                showModalMessage("Authenticating...", 0);
                const userCredential = await signInAnonymously(window.auth);
                console.log('Anonymous authentication successful', userCredential.user.uid);
                window.currentUser = userCredential.user;

                // Wait a moment for the auth token to be ready
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (authError) {
                console.error('Authentication failed:', authError);
                showModalMessage("Authentication failed. Please refresh the page and try again.");
                joinBtn.disabled = false;
                return;
            }
        }

        console.log('Current user authenticated:', window.currentUser.uid);

        // Check if Ronin Wallet extension is still available
        const provider = getRoninProvider();
        if (!provider) {
            showModalMessage("Ronin Wallet not detected. Please ensure it's installed and refresh the page.");
            joinBtn.disabled = false;
            return;
        }

        // Test if extension context is still valid
        try {
            await provider.request({ method: 'eth_accounts' });
        } catch (contextError) {
            if (contextError.message && contextError.message.includes('Extension context invalidated')) {
                showModalMessage("Ronin Wallet extension needs to be refreshed. Please refresh this page and try again.");
                joinBtn.disabled = false;
                return;
            }
        }

        if (!currentTournamentData || !currentTournamentId) {
            showModalMessage("No tournament selected or data loaded.");
            joinBtn.disabled = false;
            return;
        }

        // Check if user has already joined using wallet address
        const currentUserWalletAddress = currentUserAddress?.toLowerCase();
        if (!currentUserWalletAddress) {
            showModalMessage("Please connect your wallet to join the tournament.");
            joinBtn.disabled = false;
            return;
        }

        // For team tournaments, check if user is already in any team
        if (teamData && currentTournamentData.teamParticipants) {
            const existingTeam = currentTournamentData.teamParticipants.find(team =>
                team.members && team.members.includes(currentUserWalletAddress)
            );
            if (existingTeam) {
                showModalMessage("You have already joined this tournament as part of a team.");
                joinBtn.disabled = false;
                return;
            }
        } else if (currentTournamentData.participants && currentTournamentData.participants.includes(currentUserWalletAddress)) {
            showModalMessage("You have already joined this tournament.");
            joinBtn.disabled = false;
            return;
        }

        // Check tournament capacity
        const currentParticipantCount = teamData ?
            (currentTournamentData.teamParticipants?.length || 0) :
            (currentTournamentData.participants?.length || 0);

        if (currentTournamentData.maxEntries != 0 && currentParticipantCount >= currentTournamentData.maxEntries) {
            showModalMessage("Sorry, this tournament is already full.");
            joinBtn.disabled = false;
            if(currentTournamentId) await loadTournamentDetails(currentTournamentId);
            return;
        }

        const feeInfo = parseEntryFee(currentTournamentData.entryFee);
        let transactionHash = null;

        try {
            joinBtn.textContent = 'Processing...';

            // Handle paid entry
            if (feeInfo.amount > 0 && feeInfo.token !== 'FREE') {
                showModalMessage(`Processing ${feeInfo.amount} ${feeInfo.token} entry fee... Please confirm in your wallet. Note: You'll need additional RON for gas fees.`, 0);

                const provider = await getRoninProvider();
                if (!provider) {
                    throw new Error("Ronin Wallet provider not found.");
                }

                if (feeInfo.token === 'RON') {
                    const txParams = {
                        from: currentUserAddress,
                        to: currentTournamentData.creatorAddress,
                        value: feeInfo.amountHex
                    };

                    console.log('Transaction parameters:', {
                        from: txParams.from,
                        to: txParams.to,
                        value: txParams.value,
                        entryFeeAmount: feeInfo.amount,
                        entryFeeToken: feeInfo.token
                    });

                    // Check user's RON balance before attempting transaction
                    try {
                        const balance = await provider.request({
                            method: 'eth_getBalance',
                            params: [currentUserAddress, 'latest']
                        });

                        console.log('Raw balance response:', balance);

                        // Handle different balance response formats
                        let balanceInWei;
                        if (typeof balance === 'string') {
                            balanceInWei = BigInt(balance);
                        } else if (typeof balance === 'number') {
                            balanceInWei = BigInt(balance);
                        } else {
                            console.warn('Unexpected balance format:', typeof balance, balance);
                            balanceInWei = BigInt(0);
                        }

                        const balanceInRON = Number(balanceInWei) / (10**18);
                        console.log(`User RON balance: ${balanceInRON.toFixed(6)} RON (${balanceInWei.toString()} wei)`);

                        if (balanceInRON < feeInfo.amount + 0.001) { // Entry fee + minimum gas estimate
                            console.warn(`Balance check suggests insufficient funds: ${balanceInRON.toFixed(6)} RON < ${(feeInfo.amount + 0.001).toFixed(6)} RON required`);
                            // Don't throw error, just warn - balance check might be inaccurate
                            console.warn('Proceeding with transaction despite balance warning...');
                        }
                    } catch (balanceError) {
                        console.warn('Could not check balance:', balanceError);
                        // Continue with transaction attempt even if balance check fails
                    }

                    // Detect Ronin wallet version for compatibility
                    let walletVersion = 'unknown';
                    try {
                        if (window.ronin && window.ronin.version) {
                            walletVersion = window.ronin.version;
                        } else if (window.ethereum && window.ethereum.isRonin) {
                            walletVersion = 'legacy';
                        }
                        console.log(`Detected Ronin wallet version: ${walletVersion}`);

                        // Warn about older versions
                        if (walletVersion === 'legacy' || walletVersion.startsWith('1.')) {
                            console.warn('Older Ronin wallet version detected. Consider updating for better compatibility.');
                            showModalMessage(`Older Ronin wallet detected (${walletVersion}). For best experience, consider updating to version 2.6.0 or later. Transaction will proceed with compatibility mode.`, 3000);
                        }
                    } catch (e) {
                        console.warn('Could not detect wallet version');
                    }

                    try {
                        // Use different transaction methods based on wallet version
                        if (walletVersion === 'legacy' || walletVersion.startsWith('1.')) {
                            console.log('Using legacy transaction method for older Ronin wallet');
                            // For older versions, try with explicit gas limit
                            const gasLimit = await provider.request({
                                method: 'eth_estimateGas',
                                params: [txParams]
                            }).catch(() => '0x5208'); // Default 21000 gas for simple transfer

                            const txParamsWithGas = {
                                ...txParams,
                                gas: gasLimit
                            };

                            transactionHash = await provider.request({
                                method: 'eth_sendTransaction',
                                params: [txParamsWithGas]
                            });
                        } else {
                            console.log('Using modern transaction method');
                            transactionHash = await provider.request({
                                method: 'eth_sendTransaction',
                                params: [txParams]
                            });
                        }
                    } catch (txError) {
                        // Handle different types of transaction errors
                        console.error('Transaction error details:', txError);

                        // Check if extension context was invalidated (common with wallet updates/reloads)
                        if (txError.message && txError.message.includes('Extension context invalidated')) {
                            throw new Error("Ronin Wallet extension was reloaded or updated. Please refresh this page and try again.");
                        }

                        // Check if user rejected the transaction
                        if (txError.code === 4001) {
                            throw new Error("Transaction was rejected by user.");
                        }

                        // Check if it's insufficient funds
                        if (txError.message && txError.message.toLowerCase().includes('insufficient funds')) {
                            throw new Error("Insufficient RON balance. Please add more RON to your wallet for the entry fee and gas costs.");
                        }

                        // Check if it's a gas estimation error
                        if (txError.message && (
                            txError.message.includes('gas required exceeds allowance') ||
                            txError.message.includes('out of gas') ||
                            txError.message.includes('gas limit')
                        )) {
                            throw new Error("Transaction failed due to gas limit. Please try again or contact support.");
                        }

                        // For network or RPC errors, provide helpful message
                        if (txError.message && (
                            txError.message.includes('network') ||
                            txError.message.includes('rpc') ||
                            txError.message.includes('connection')
                        )) {
                            throw new Error("Network error. Please check your connection and try again.");
                        }

                        // Special handling for older wallet versions
                        if (walletVersion === 'legacy' || walletVersion.startsWith('1.')) {
                            if (txError.message && txError.message.includes('ronin_getFreeGasRequests')) {
                                throw new Error("Transaction failed. Your Ronin wallet version may not be fully compatible. Please update to the latest version or ensure you have sufficient RON for gas fees.");
                            }
                        }

                        // For any other error, provide a generic but helpful message
                        throw new Error(`Transaction failed: ${txError.message || 'Unknown error'}. Please ensure you have sufficient RON for both the entry fee and gas costs.`);
                    }

                    if (!transactionHash) {
                        throw new Error("Transaction failed to send. Please try again.");
                    }

                    showModalMessage(`Entry fee transaction sent! Hash: ${truncateAddress(transactionHash)}. Waiting for confirmation...`, 0);

                    // Ensure user is authenticated before calling Cloud Function
                    if (!window.currentUser) {
                        throw new Error("Authentication required. Please refresh the page and try again.");
                    }

                    console.log('Calling verifyPayment with user:', window.currentUser.uid);

                    // Wait for auth token to be ready and call verifyPayment function
                    await window.currentUser.getIdToken(true); // Force refresh the token
                    const verifyPayment = httpsCallable(functions, 'verifyPayment');
                    const verificationResult = await verifyPayment({
                        transactionHash,
                        tournamentId: currentTournamentId
                    });

                    if (verificationResult.data.status === 'failed') {
                        throw new Error(verificationResult.data.message);
                    }

                    if (verificationResult.data.status === 'processing') {
                        showModalMessage(`Transaction is being processed. ${verificationResult.data.message}`, 0);
                        return;
                    }
                } else {
                    // ERC20 Token handling would go here
                    throw new Error(`ERC20 token (${feeInfo.token}) transactions require Cloud Function implementation.`);
                }
            } else {
                showModalMessage(`Joining "${currentTournamentData.tournamentName}" (Free Entry)...`, 0);
                // For free tournaments, we don't need payment verification
                transactionHash = null;
            }

            // Update Firestore
            const tournamentRef = doc(db, 'tournaments', currentTournamentId);

            try {
                await runTransaction(db, async (transaction) => {
                    const sfDoc = await transaction.get(tournamentRef);
                    if (!sfDoc.exists) {
                        throw new Error("Tournament no longer exists.");
                    }

                    const currentData = sfDoc.data();

                    if (teamData) {
                        // Handle team tournament registration
                        const existingTeam = (currentData.teamParticipants || []).find(team =>
                            team.members && team.members.includes(currentUserWalletAddress)
                        );
                        if (existingTeam) {
                            throw new Error("You have already joined this tournament as part of a team.");
                        }

                        if (currentData.maxEntries != 0 && (currentData.teamParticipants?.length || 0) >= currentData.maxEntries) {
                            throw new Error("Tournament is full.");
                        }

                        // Create team object with additional server-side validation
                        const teamId = `team_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                        const teammates = teamData.teammates || [];

                        // Server-side validation: Check for duplicate addresses and self-inclusion
                        const allTeamMembers = [currentUserWalletAddress, ...teammates];
                        const uniqueMembers = new Set(allTeamMembers.map(addr => addr.toLowerCase()));

                        if (uniqueMembers.size !== allTeamMembers.length) {
                            throw new Error("Duplicate wallet addresses detected in team. Each team member must have a unique wallet address.");
                        }

                        // Validate team size based on tournament format
                        const expectedTeamSize = currentData.tournamentFormat === '2v2' ? 2 : currentData.tournamentFormat === '3v3' ? 3 : 0;
                        if (expectedTeamSize > 0 && allTeamMembers.length !== expectedTeamSize) {
                            throw new Error(`${currentData.tournamentFormat} tournaments require exactly ${expectedTeamSize} team members. Found ${allTeamMembers.length}.`);
                        }

                        // Check if any team member is already in another team in this tournament
                        const existingTeams = currentData.teamParticipants || [];
                        for (const existingTeam of existingTeams) {
                            if (existingTeam.members) {
                                const conflictingMember = allTeamMembers.find(member =>
                                    existingTeam.members.some(existingMember =>
                                        existingMember.toLowerCase() === member.toLowerCase()
                                    )
                                );
                                if (conflictingMember) {
                                    throw new Error(`Wallet address ${conflictingMember} is already part of another team in this tournament.`);
                                }
                            }
                        }

                        const teamMembers = allTeamMembers;
                        const teamName = teamData.teamName || `Team ${(currentData.teamParticipants?.length || 0) + 1}`;

                        const newTeam = {
                            id: teamId,
                            name: teamName,
                            members: teamMembers,
                            captain: currentUserWalletAddress,
                            joinType: teamData.joinType,
                            createdAt: Timestamp.now()
                        };

                        transaction.update(tournamentRef, {
                            teamParticipants: arrayUnion(newTeam),
                            participantCount: increment(1),
                            ...(transactionHash && {
                                transactionHashes: arrayUnion({
                                    user: currentUserAddress,
                                    hash: transactionHash,
                                    timestamp: Timestamp.now()
                                })
                            })
                        });
                    } else {
                        // Handle individual tournament registration
                        if (currentData.participants.includes(currentUserWalletAddress)) {
                            throw new Error("You have already joined this tournament.");
                        }

                        if (currentData.maxEntries != 0 && currentData.participantCount >= currentData.maxEntries) {
                            throw new Error("Tournament is full.");
                        }

                        transaction.update(tournamentRef, {
                            participants: arrayUnion(currentUserWalletAddress),
                            participantCount: increment(1),
                            ...(transactionHash && {
                                transactionHashes: arrayUnion({
                                    user: currentUserAddress,
                                    hash: transactionHash,
                                    timestamp: Timestamp.now()
                                })
                            })
                        });
                    }
                });

                showModalMessage(`Successfully joined "${currentTournamentData.tournamentName}"!`);
                await loadTournamentDetails(currentTournamentId);
            } catch (transactionError) {
                if (transactionError.code === 'permission-denied') {
                    throw new Error("You don't have permission to join this tournament. Please make sure you're connected.");
                }
                throw transactionError;
            }

        } catch (error) {
            console.error("Join tournament error:", error);
            showModalMessage(`Failed to join: ${error.message || 'An error occurred.'}`);
            if(currentTournamentId) {
                await loadTournamentDetails(currentTournamentId);
            }
        } finally {
            joinBtn.disabled = false;
            joinBtn.textContent = 'Join Tournament';
        }
    }

    async function loadMyTournaments() {
        if (!currentUserAddress || !window.currentUser) {
            document.getElementById('createdTournamentsList').innerHTML = '<p class="text-gray-500">Connect your wallet to see your tournaments.</p>';
            document.getElementById('joinedTournamentsList').innerHTML = '<p class="text-gray-500">Connect your wallet to see tournaments you joined.</p>';
            return;
        }

        const createdListEl = document.getElementById('createdTournamentsList');
        const joinedListEl = document.getElementById('joinedTournamentsList');
        createdListEl.innerHTML = '<p class="text-gray-400">Loading your created tournaments...</p>';
        joinedListEl.innerHTML = '<p class="text-gray-400">Loading tournaments you\'ve joined...</p>';

        const currentUserWalletAddress = currentUserAddress.toLowerCase();

        try {
            // Created Tournaments (use wallet address for creator)
            const createdSnapshot = await getDocs(query(
                collection(db, 'tournaments'),
                where('creatorAddress', '==', currentUserAddress),
                orderBy('createdAt', 'desc')
            ));
            createdListEl.innerHTML = ''; // Clear loading
            if (createdSnapshot.empty) {
                createdListEl.innerHTML = '<p class="text-gray-500">You haven\'t created any tournaments yet.</p>';
            } else {
                createdSnapshot.forEach(doc => {
                    createdListEl.appendChild(createTournamentCard({ id: doc.id, ...doc.data() }));
                });
            }

            // Joined Tournaments (use wallet address for participants)
            const joinedSnapshot = await getDocs(query(
                collection(db, 'tournaments'),
                where('participants', 'array-contains', currentUserWalletAddress),
                orderBy('startDate', 'desc')
            ));
            joinedListEl.innerHTML = ''; // Clear loading
            if (joinedSnapshot.empty) {
                joinedListEl.innerHTML = '<p class="text-gray-500">You haven\'t joined any tournaments yet.</p>';
            } else {
                joinedSnapshot.forEach(doc => {
                    // Avoid re-listing if also created by user (optional, depends on desired UX)
                    if (doc.data().creatorAddress !== currentUserAddress) {
                        joinedListEl.appendChild(createTournamentCard({ id: doc.id, ...doc.data() }, 'joinedTournamentsList'));
                    }
                });
                if (joinedListEl.children.length === 0 && !joinedSnapshot.empty) { // If all joined were also created
                    joinedListEl.innerHTML = '<p class="text-gray-500">Tournaments you created are listed above.</p>';
                }
            }

        } catch (error) {
            console.error("Error loading my tournaments:", error);
            createdListEl.innerHTML = '<p class="text-red-400">Error loading created tournaments.</p>';
            joinedListEl.innerHTML = '<p class="text-red-400">Error loading joined tournaments.</p>';
        }
    }

    function handleSearch() {
        const searchTerm = searchInput.value.trim();
        // For now, this will just re-trigger a load of upcoming tournaments with a simple name filter.
        // A more robust search would involve specific backend indexing (e.g., Algolia).
        if (searchTerm.length > 2 || searchTerm.length === 0) { // Search on 3+ chars or empty to reset
             loadUpcomingTournaments({ searchTerm: searchTerm });
             // You might want to clear featured or other sections, or have search results on a dedicated page/view
        }
    }


    // Suppress Ronin wallet console errors for free gas requests and extension context issues
    const originalConsoleError = console.error;
    console.error = function(...args) {
        // Filter out the specific Ronin free gas request errors and extension context errors
        const message = args.join(' ');
        if (message.includes('ronin_getFreeGasRequests') ||
            message.includes('JsonRpcEngine: Response has no error or result') ||
            message.includes('content-script.js') ||
            message.includes('Extension context invalidated') ||
            (message.includes('Error:') && message.includes('ronin_getFreeGasRequests'))) {
            // Silently ignore these errors as they're expected when free gas isn't available or extension is reloaded
            return;
        }
        // Log all other errors normally
        originalConsoleError.apply(console, args);
    };

    // Also suppress errors from the window error handler for Ronin wallet
    const originalWindowError = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
        if (typeof message === 'string' && (
            message.includes('ronin_getFreeGasRequests') ||
            message.includes('JsonRpcEngine: Response has no error or result') ||
            message.includes('Extension context invalidated') ||
            source.includes('content-script.js')
        )) {
            // Silently ignore Ronin wallet free gas errors and extension context errors
            return true;
        }
        // Handle all other errors normally
        if (originalWindowError) {
            return originalWindowError.call(this, message, source, lineno, colno, error);
        }
        return false;
    };

    // Function to check if all required components are loaded
    function checkComponentsLoaded() {
        const components = {
            React: !!window.React,
            ReactDOM: !!window.ReactDOM,
            BracketView: !!window.BracketView,
            AdminControlPanel: !!window.AdminControlPanel,
            NotificationBadge: !!window.NotificationBadge,
            NotificationList: !!window.NotificationList
        };

        console.log('Component availability check:', components);

        // Check for missing critical components
        const missing = Object.entries(components)
            .filter(([name, available]) => !available)
            .map(([name]) => name);

        if (missing.length > 0) {
            console.warn('Missing components:', missing);
        }

        return components;
    }

    // Debug function for admin controls
    function debugAdminControls() {
        console.log('=== ADMIN CONTROLS DEBUG ===');
        console.log('Current user address:', currentUserAddress);
        console.log('Current tournament data:', currentTournamentData);
        console.log('Components available:', checkComponentsLoaded());

        if (currentTournamentData) {
            console.log('Creator address:', currentTournamentData.creatorAddress);
            console.log('Address match:', currentUserAddress?.toLowerCase() === currentTournamentData.creatorAddress?.toLowerCase());
        }

        // Try to manually render admin controls
        if (window.AdminControlPanel && currentTournamentData) {
            const container = document.getElementById('adminControlContainer');
            container.innerHTML = '<p class="text-blue-400">Attempting manual render...</p>';

            try {
                const root = ReactDOM.createRoot(container);
                const adminPanel = React.createElement(window.AdminControlPanel, {
                    tournament: currentTournamentData,
                    currentUserAddress: currentUserAddress
                });
                root.render(adminPanel);
                console.log('✅ Manual admin controls render successful');
            } catch (error) {
                console.error('❌ Manual admin controls render failed:', error);
                container.innerHTML = `<p class="text-red-400">Manual render failed: ${error.message}</p>`;
            }
        } else {
            console.error('Cannot manually render - missing AdminControlPanel or tournament data');
        }
    }

    // --- Event Listeners ---
    document.addEventListener('DOMContentLoaded', () => {
        // Check component availability
        checkComponentsLoaded();

        // Check for Firebase config
        if (firebaseConfig.apiKey === "YOUR_API_KEY") {
            showModalMessage("Firebase is not configured. Please update firebaseConfig in the script.", 0);
            console.error("Firebase is not configured!");
            // Disable forms or buttons that require Firebase
            const createFormButton = document.getElementById('submitCreateTournamentBtn');
            if (createFormButton) createFormButton.disabled = true;
            return; // Stop further execution if Firebase isn't set up
        }


        checkInitialWalletConnection().then(setupWalletListeners);

        connectWalletBtn.addEventListener('click', connectWallet);

        document.body.addEventListener('click', (event) => {
            const target = event.target.closest('[data-navigate]');
            if (target) {
                event.preventDefault();
                const pageId = target.dataset.navigate;
                const tournamentId = target.dataset.tournamentId; // Used by tournament cards
                navigateTo(pageId, { tournamentId });
            }

            const gameFilterTarget = event.target.closest('[data-game-filter]');
            if (gameFilterTarget) {
                event.preventDefault();
                const gameName = gameFilterTarget.dataset.gameFilter;
                showModalMessage(`Filtering by ${gameName}...`);
                loadUpcomingTournaments({ gameName: gameName });
                // Potentially scroll to upcoming tournaments section or update a title
                document.getElementById('upcomingTournamentsContainer').scrollIntoView({ behavior: 'smooth' });
            }
        });

        if (createTournamentForm) {
            createTournamentForm.addEventListener('submit', handleCreateTournament);
        }

        const joinBtn = document.getElementById('joinTournamentBtn');
        if(joinBtn) {
            joinBtn.addEventListener('click', handleJoinTournament);
        }

        if(searchInput) {
            searchInput.addEventListener('input', handleSearch); // Or 'change' or a dedicated search button
        }

        // Team tournament info toggle
        const tournamentFormatSelect = document.getElementById('tournamentFormat');
        if (tournamentFormatSelect) {
            tournamentFormatSelect.addEventListener('change', toggleTeamFormationSection);
        }

        // Team formation modal event listeners
        const closeTeamModal = document.getElementById('closeTeamModal');
        const cancelJoinTeam = document.getElementById('cancelJoinTeam');
        const confirmJoinTeam = document.getElementById('confirmJoinTeam');
        const joinWithTeamRadio = document.getElementById('joinWithTeam');
        const joinSoloRadio = document.getElementById('joinSolo');

        if (closeTeamModal) {
            closeTeamModal.addEventListener('click', hideTeamFormationModal);
        }
        if (cancelJoinTeam) {
            cancelJoinTeam.addEventListener('click', hideTeamFormationModal);
        }
        if (confirmJoinTeam) {
            confirmJoinTeam.addEventListener('click', async () => {
                try {
                    const teamData = getTeamFormationData();
                    hideTeamFormationModal();
                    await processJoinTournament(teamData);
                } catch (error) {
                    showModalMessage(`Error: ${error.message}`);
                }
            });
        }
        if (joinWithTeamRadio && joinSoloRadio) {
            joinWithTeamRadio.addEventListener('change', toggleTeamFormInterface);
            joinSoloRadio.addEventListener('change', toggleTeamFormInterface);
        }

        // Initial page load logic (e.g., from hash or default to home)
        const initialPage = window.location.hash ? window.location.hash.substring(1).split('?')[0] : 'home';
        // Check for tournamentId in hash for direct links, e.g. #tournament-details?id=XYZ
        let initialTournamentId = null;
        if (window.location.hash.includes('?id=')) {
            initialTournamentId = window.location.hash.split('?id=')[1];
        }

        navigateTo(initialPage, { tournamentId: initialTournamentId });


    });

</script>
<script>
  console.log('React version:', React.version);
  console.log('ReactDOM version:', ReactDOM.version);
</script>
</body>
</html>

