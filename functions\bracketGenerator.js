// bracketGenerator.js
// Maintained by: Gemini
// Last Updated: May 19, 2025

/**
 * @typedef {Object} Participant
 * @property {string} id - Unique identifier (e.g., Ronin address, team ID).
 * @property {string} name - Display name of the participant or team.
 * @property {number} [seed] - Optional seed value (lower is better). Participants will be sorted by this if provided.
 * @property {string[]} [members] - For teams, an array of member IDs or names.
 * @property {any} [metadata] - Any other custom data.
 */

/**
 * @typedef {Object} Match
 * @property {string} id - Unique match ID (e.g., "R1M1", "ULR1M1").
 * @property {number} roundIndex - 0-based index of the round this match belongs to.
 * @property {number} matchIndexInRound - 0-based index of this match within its round.
 * @property {string|null} participant1Id - ID of the first participant/team. Null if BYE or TBD.
 * @property {string|null} participant2Id - ID of the second participant/team. Null if BYE or TBD.
 * @property {string|null} winnerId - ID of the winning participant/team. Updated after match completion.
 * @property {string|null} loserId - ID of the losing participant/team. Updated after match completion.
 * @property {string} status - e.g., 'PENDING', 'BYE', 'PARTICIPANT1_WIN', 'PARTICIPANT2_WIN', 'IN_PROGRESS'.
 * @property {Object|null} score - e.g., { participant1: 0, participant2: 0 }.
 * @property {string|null} nextMatchId - ID of the match the winner advances to (for elimination formats).
 * @property {string|null} nextLoserMatchId - ID of the match the loser advances to (for double elimination).
 * @property {boolean} [isUpperBracket] - True if the match is in the upper bracket (for double elimination).
 * @property {string} [identifier] - A human-readable identifier, e.g., "Winners Final".
 */

/**
 * @typedef {Object} Round
 * @property {string} id - Unique round ID (e.g., "R1", "ULR1").
 * @property {string} name - Display name of the round (e.g., "Round 1", "Winners Round 1", "Losers Round 1").
 * @property {Match[]} matches - Array of matches in this round.
 */

/**
 * @typedef {Object} BracketData
 * @property {string} format - The tournament format (e.g., 'single-elimination', 'double-elimination', 'round-robin').
 * @property {Participant[]} participants - The initial list of participants/teams, potentially sorted by seed.
 * @property {Round[]} rounds - Array of rounds in the bracket.
 * @property {Object<string, Match>} matchesById - A map of all matches by their ID for easy lookup.
 * @property {Object} [metadata] - Additional data specific to the bracket type (e.g., total rounds for double elim).
 */

// --- Helper Functions ---

/**
 * Shuffles an array in place using the Fisher-Yates algorithm.
 * @param {Array<any>} array - The array to shuffle.
 * @returns {Array<any>} The shuffled array.
 */
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

/**
 * Generates a simple unique ID. For production, consider UUIDs.
 * @param {string} [prefix=''] - Prefix for the ID.
 * @returns {string} A unique ID string.
 */
function generateUniqueId(prefix = '') {
    return prefix + Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
}

/**
 * Randomly shuffles participants for random seeding.
 * @param {Participant[]} participants - Array of participants.
 * @returns {Participant[]} Shuffled participants.
 */
function prepareParticipants(participants) {
    // Always shuffle participants for random seeding
    return shuffleArray([...participants]);
}


// --- Single Elimination Bracket Generator ---

/**
 * Generates a single elimination bracket.
 * @param {Participant[]} initialParticipants - Array of participant objects.
 * @returns {BracketData} The generated bracket data.
 */
function generateSingleEliminationBracket(initialParticipants) {
    if (!initialParticipants || initialParticipants.length < 2) {
        throw new Error("Single elimination bracket requires at least 2 participants.");
    }

    const participants = prepareParticipants(initialParticipants);
    const numParticipants = participants.length;
    const roundsData = [];
    const matchesById = {};

    let matchIdCounter = 1;
    let currentParticipantCount = numParticipants;

    // Generate rounds following the half rule
    while (currentParticipantCount > 1) {
        const roundIndex = roundsData.length;
        const roundId = `R${roundIndex + 1}`;
        const roundName = `Round ${roundIndex + 1}`;
        const roundMatches = [];

        // Calculate matches for this round using half rule
        const numMatches = Math.floor(currentParticipantCount / 2);
        const hasOddParticipant = currentParticipantCount % 2 === 1;

        // Create matches for this round
        for (let matchIndex = 0; matchIndex < numMatches; matchIndex++) {
            const matchId = `${roundId}M${matchIdCounter++}`;

            const match = {
                id: matchId,
                roundIndex,
                matchIndexInRound: matchIndex,
                participant1Id: null,
                participant2Id: null,
                winnerId: null,
                loserId: null,
                status: 'PENDING',
                score: null,
                nextMatchId: null,
            };

            roundMatches.push(match);
            matchesById[match.id] = match;
        }

        roundsData.push({ id: roundId, name: roundName, matches: roundMatches });

        // Calculate participants for next round: winners + any bye
        currentParticipantCount = numMatches + (hasOddParticipant ? 1 : 0);
    }

    // Set up match progression links (nextMatchId)
    for (let roundIndex = 0; roundIndex < roundsData.length - 1; roundIndex++) {
        const currentRound = roundsData[roundIndex];
        const nextRound = roundsData[roundIndex + 1];

        currentRound.matches.forEach((match, matchIndex) => {
            const nextMatchIndex = Math.floor(matchIndex / 2);
            if (nextMatchIndex < nextRound.matches.length) {
                match.nextMatchId = nextRound.matches[nextMatchIndex].id;
            }
        });
    }

    // Populate first round with actual participants
    if (roundsData.length > 0) {
        const firstRound = roundsData[0];
        let participantIndex = 0;

        firstRound.matches.forEach(match => {
            if (participantIndex < participants.length) {
                match.participant1Id = participants[participantIndex].id;
                participantIndex++;
            }
            if (participantIndex < participants.length) {
                match.participant2Id = participants[participantIndex].id;
                participantIndex++;
            }
        });

        // Handle bye participant if odd number
        if (participantIndex < participants.length) {
            // Find the last match in round 2 and give it the bye participant
            if (roundsData.length > 1) {
                const secondRound = roundsData[1];
                const lastMatch = secondRound.matches[secondRound.matches.length - 1];
                if (!lastMatch.participant2Id) {
                    lastMatch.participant2Id = participants[participantIndex].id;
                }
            }
        }
    }

    // Populate subsequent rounds with TBD placeholders
    for (let roundIndex = 1; roundIndex < roundsData.length; roundIndex++) {
        const currentRound = roundsData[roundIndex];
        const previousRound = roundsData[roundIndex - 1];

        currentRound.matches.forEach((match, matchIndex) => {
            // Each match gets winners from two previous matches
            const prevMatch1Index = matchIndex * 2;
            const prevMatch2Index = matchIndex * 2 + 1;

            if (prevMatch1Index < previousRound.matches.length) {
                if (!match.participant1Id) {
                    match.participant1Id = `TBD_${previousRound.matches[prevMatch1Index].id}`;
                }
            }

            if (prevMatch2Index < previousRound.matches.length) {
                if (!match.participant2Id) {
                    match.participant2Id = `TBD_${previousRound.matches[prevMatch2Index].id}`;
                }
            }
        });
    }

    // Link matches to create bracket progression
    for (let roundIndex = 0; roundIndex < roundsData.length - 1; roundIndex++) {
        const currentRound = roundsData[roundIndex];
        const nextRound = roundsData[roundIndex + 1];

        currentRound.matches.forEach((match, matchIndex) => {
            const nextMatchIndex = Math.floor(matchIndex / 2);
            if (nextMatchIndex < nextRound.matches.length) {
                match.nextMatchId = nextRound.matches[nextMatchIndex].id;
            }
        });
    }

    // Set proper round names
    if (roundsData.length > 0) {
        const finalRound = roundsData[roundsData.length - 1];
        if (finalRound.matches.length === 1) {
            finalRound.name = "Final";
            matchesById[finalRound.matches[0].id].identifier = "Final";
        }
        if (roundsData.length > 1) {
            const semiFinalRound = roundsData[roundsData.length - 2];
            if (semiFinalRound.matches.length <= 2) {
                semiFinalRound.name = "Semi-Finals";
                semiFinalRound.matches.forEach((match, index) => {
                    matchesById[match.id].identifier = `Semi-Final ${index + 1}`;
                });
            }
        }
    }

    return {
        format: 'single-elimination',
        participants: initialParticipants,
        rounds: roundsData,
        matchesById,
        metadata: { numRoundsActual: roundsData.length }
    };
}


// --- Double Elimination Bracket Generator ---
/**
 * Generates a double elimination bracket.
 * @param {Participant[]} initialParticipants - Array of participant objects.
 * @returns {BracketData} The generated bracket data.
 */
function generateDoubleEliminationBracket(initialParticipants) {
    if (!initialParticipants || initialParticipants.length < 3) {
        throw new Error("Double elimination bracket requires at least 3 participants.");
    }

    const participants = prepareParticipants(initialParticipants);
    const numParticipants = participants.length;
    const roundsData = [];
    const matchesById = {};

    // --- Upper Bracket Generation ---
    let matchIdCounter = 1;
    let upperBracketRounds = [];
    let currentParticipantCount = numParticipants;

    // Generate upper bracket rounds following half rule (same as single elimination)
    while (currentParticipantCount > 1) {
        const roundIndex = upperBracketRounds.length;
        const roundId = `UBR${roundIndex + 1}`;
        const roundName = `Winners Round ${roundIndex + 1}`;
        const roundMatches = [];

        // Calculate matches for this round using half rule
        const numMatches = Math.floor(currentParticipantCount / 2);

        // Create matches for this round
        for (let matchIndex = 0; matchIndex < numMatches; matchIndex++) {
            const matchId = `${roundId}M${matchIdCounter++}`;

            const match = {
                id: matchId,
                roundIndex,
                matchIndexInRound: matchIndex,
                participant1Id: null,
                participant2Id: null,
                winnerId: null,
                loserId: null,
                status: 'PENDING',
                score: null,
                nextMatchId: null,
                nextLoserMatchId: null, // Will be set later for lower bracket
                isUpperBracket: true,
            };

            roundMatches.push(match);
            matchesById[match.id] = match;
        }

        upperBracketRounds.push({ id: roundId, name: roundName, matches: roundMatches });

        // Calculate participants for next round: winners + any bye
        const hasOddParticipant = currentParticipantCount % 2 === 1;
        currentParticipantCount = numMatches + (hasOddParticipant ? 1 : 0);
    }

    // Set up upper bracket match progression links (nextMatchId)
    for (let roundIndex = 0; roundIndex < upperBracketRounds.length - 1; roundIndex++) {
        const currentRound = upperBracketRounds[roundIndex];
        const nextRound = upperBracketRounds[roundIndex + 1];

        currentRound.matches.forEach((match, matchIndex) => {
            const nextMatchIndex = Math.floor(matchIndex / 2);
            if (nextMatchIndex < nextRound.matches.length) {
                match.nextMatchId = nextRound.matches[nextMatchIndex].id;
            }
        });
    }

    // Populate first upper bracket round with actual participants
    if (upperBracketRounds.length > 0) {
        const firstRound = upperBracketRounds[0];
        let participantIndex = 0;

        firstRound.matches.forEach(match => {
            if (participantIndex < participants.length) {
                match.participant1Id = participants[participantIndex].id;
                participantIndex++;
            }
            if (participantIndex < participants.length) {
                match.participant2Id = participants[participantIndex].id;
                participantIndex++;
            }
        });

        // Handle bye participant if odd number
        if (participantIndex < participants.length) {
            // Find the last match in round 2 and give it the bye participant
            if (upperBracketRounds.length > 1) {
                const secondRound = upperBracketRounds[1];
                const lastMatch = secondRound.matches[secondRound.matches.length - 1];
                if (!lastMatch.participant2Id) {
                    lastMatch.participant2Id = participants[participantIndex].id;
                }
            }
        }
    }

    // Populate subsequent upper bracket rounds with TBD placeholders
    for (let roundIndex = 1; roundIndex < upperBracketRounds.length; roundIndex++) {
        const currentRound = upperBracketRounds[roundIndex];
        const previousRound = upperBracketRounds[roundIndex - 1];

        currentRound.matches.forEach((match, matchIndex) => {
            // Each match gets winners from two previous matches
            const prevMatch1Index = matchIndex * 2;
            const prevMatch2Index = matchIndex * 2 + 1;

            if (prevMatch1Index < previousRound.matches.length) {
                if (!match.participant1Id) {
                    match.participant1Id = `TBD_${previousRound.matches[prevMatch1Index].id}`;
                }
            }

            if (prevMatch2Index < previousRound.matches.length) {
                if (!match.participant2Id) {
                    match.participant2Id = `TBD_${previousRound.matches[prevMatch2Index].id}`;
                }
            }
        });
    }

    // Calculate number of upper rounds for lower bracket generation
    const numUpperRounds = upperBracketRounds.length;

    // Link upper bracket matches to next round after all rounds are created
    for (let roundIndex = 0; roundIndex < upperBracketRounds.length - 1; roundIndex++) {
        const currentRound = upperBracketRounds[roundIndex];
        const nextRound = upperBracketRounds[roundIndex + 1];

        if (currentRound && nextRound) {
            currentRound.matches.forEach((match, matchIndex) => {
                // Skip bye matches
                if (match.status === 'BYE') return;

                const nextRoundMatchIndex = Math.floor(matchIndex / 2);
                if (nextRoundMatchIndex < nextRound.matches.length) {
                    const nextMatch = nextRound.matches[nextRoundMatchIndex];
                    match.nextMatchId = nextMatch.id;
                }
            });
        }
    }

    // Link the final upper bracket match to Grand Final
    if (upperBracketRounds.length > 0) {
        const finalUpperRound = upperBracketRounds[upperBracketRounds.length - 1];
        if (finalUpperRound.matches.length > 0) {
            const finalUpperMatch = finalUpperRound.matches[0];
            // Find the Grand Final match
            const grandFinalMatch = Object.values(matchesById).find(match =>
                match.identifier === "Grand Final"
            );
            if (grandFinalMatch) {
                finalUpperMatch.nextMatchId = grandFinalMatch.id;
            }
        }
    }

    // --- Lower Bracket Generation ---
    let lowerBracketRounds = [];

    // Calculate number of lower bracket rounds needed
    // For double elimination: 2 * (numUpperRounds - 1) rounds
    const numLowerRounds = Math.max(1, 2 * (numUpperRounds - 1));

    // Generate lower bracket structure with proper match counts
    for (let lbRoundIndex = 0; lbRoundIndex < numLowerRounds; lbRoundIndex++) {
        const roundId = `LBR${lbRoundIndex + 1}`;
        const roundName = `Losers Round ${lbRoundIndex + 1}`;
        const roundMatches = [];

        // Determine number of matches for this lower bracket round
        let numMatches;

        if (lbRoundIndex === 0) {
            // First lower bracket round: gets losers from first upper bracket round
            const firstUpperRoundMatches = upperBracketRounds[0]?.matches.length || 0;
            // For first LB round, we need half as many matches since losers pair up
            numMatches = Math.ceil(firstUpperRoundMatches / 2);
        } else if (lbRoundIndex === numLowerRounds - 1) {
            // Last round is always the losers final (1 match)
            numMatches = 1;
        } else {
            // Alternating pattern: odd rounds (1st, 3rd, 5th) get upper bracket losers + advance lower bracket
            // Even rounds (2nd, 4th, 6th) only advance lower bracket winners
            if (lbRoundIndex % 2 === 0) {
                // Odd-numbered rounds (LBR1, LBR3, LBR5) - receive upper bracket losers + advance lower bracket
                const prevRoundMatches = lowerBracketRounds[lbRoundIndex - 1]?.matches.length || 0;
                numMatches = prevRoundMatches;
            } else {
                // Even-numbered rounds (LBR2, LBR4, LBR6) - advance lower bracket winners only
                const prevRoundMatches = lowerBracketRounds[lbRoundIndex - 1]?.matches.length || 0;
                numMatches = Math.ceil(prevRoundMatches / 2);
            }
        }

        // Ensure at least 1 match if we're creating a round
        numMatches = Math.max(1, numMatches);

        for (let matchIndex = 0; matchIndex < numMatches; matchIndex++) {
            const matchId = `${roundId}M${matchIdCounter++}`;
            const match = {
                id: matchId,
                roundIndex: lbRoundIndex,
                matchIndexInRound: matchIndex,
                participant1Id: null, // Will be populated from upper bracket losers or previous LB round
                participant2Id: null, // Will be populated from upper bracket losers or previous LB round
                winnerId: null,
                loserId: null,
                status: 'PENDING',
                score: null,
                nextMatchId: null,
                nextLoserMatchId: null,
                isUpperBracket: false,
            };

            roundMatches.push(match);
            matchesById[match.id] = match;
        }

        if (roundMatches.length > 0) {
            lowerBracketRounds.push({ id: roundId, name: roundName, matches: roundMatches });
        }
    }

    // Link lower bracket matches to next round
    for (let lbRoundIndex = 0; lbRoundIndex < lowerBracketRounds.length - 1; lbRoundIndex++) {
        const currentRound = lowerBracketRounds[lbRoundIndex];
        const nextRound = lowerBracketRounds[lbRoundIndex + 1];

        if (currentRound && nextRound && currentRound.matches.length > 0 && nextRound.matches.length > 0) {
            currentRound.matches.forEach((match, index) => {
                const nextRoundMatchIndex = Math.floor(index / 2);
                if (nextRoundMatchIndex < nextRound.matches.length) {
                    const targetMatch = nextRound.matches[nextRoundMatchIndex];
                    match.nextMatchId = targetMatch.id;
                }
            });
        }
    }

    // Initialize lower bracket matches with TBD placeholders for upper bracket losers
    upperBracketRounds.forEach((ubRound, ubRoundIndex) => {
        ubRound.matches.forEach((ubMatch, ubMatchIndex) => {
            // Skip bye matches
            if (ubMatch.status === 'BYE') return;

            if (ubRoundIndex === 0) {
                // First upper bracket round losers go to first lower bracket round
                if (lowerBracketRounds.length > 0 && lowerBracketRounds[0].matches.length > 0) {
                    // Pair up losers: matches 0,1 -> LBR1 match 0; matches 2,3 -> LBR1 match 1, etc.
                    const targetMatchIndex = Math.floor(ubMatchIndex / 2);
                    if (targetMatchIndex < lowerBracketRounds[0].matches.length) {
                        const targetLbMatch = lowerBracketRounds[0].matches[targetMatchIndex];
                        ubMatch.nextLoserMatchId = targetLbMatch.id;

                        // Initialize LBR1 match with TBD placeholder for this upper bracket loser
                        if (ubMatchIndex % 2 === 0) {
                            // First match of pair goes to participant1
                            targetLbMatch.participant1Id = `TBD_${ubMatch.id}`;
                        } else {
                            // Second match of pair goes to participant2
                            targetLbMatch.participant2Id = `TBD_${ubMatch.id}`;
                        }
                    }
                }
            } else if (ubRoundIndex === upperBracketRounds.length - 1) {
                // Winners Final loser goes to Losers Final
                const losersFinalRound = lowerBracketRounds[lowerBracketRounds.length - 1];
                if (losersFinalRound && losersFinalRound.matches.length > 0) {
                    ubMatch.nextLoserMatchId = losersFinalRound.matches[0].id;

                    // Initialize Losers Final with TBD placeholder for Winners Final loser
                    const losersFinalMatch = losersFinalRound.matches[0];
                    if (losersFinalMatch.participant2Id === null) {
                        losersFinalMatch.participant2Id = `TBD_${ubMatch.id}`;
                    }
                }
            } else {
                // Middle upper bracket rounds feed into appropriate lower bracket rounds
                // Correct formula: UB round N losers go to LB round (2*N)
                // This ensures proper alternating pattern in lower bracket
                const targetLbRoundIndex = ubRoundIndex * 2;
                if (targetLbRoundIndex >= 0 && targetLbRoundIndex < lowerBracketRounds.length) {
                    const targetLbRound = lowerBracketRounds[targetLbRoundIndex];
                    if (targetLbRound && targetLbRound.matches.length > 0) {
                        const targetMatchIndex = Math.min(ubMatchIndex, targetLbRound.matches.length - 1);
                        const targetLbMatch = targetLbRound.matches[targetMatchIndex];
                        ubMatch.nextLoserMatchId = targetLbMatch.id;

                        // Initialize lower bracket match with TBD placeholder for this upper bracket loser
                        if (targetLbMatch.participant2Id === null) {
                            targetLbMatch.participant2Id = `TBD_${ubMatch.id}`;
                        } else if (targetLbMatch.participant1Id === null) {
                            targetLbMatch.participant1Id = `TBD_${ubMatch.id}`;
                        }
                    }
                }
            }
        });
    });

    // Initialize lower bracket matches that receive winners from previous LB rounds with TBD placeholders
    for (let lbRoundIndex = 1; lbRoundIndex < lowerBracketRounds.length; lbRoundIndex++) {
        const currentRound = lowerBracketRounds[lbRoundIndex];
        const prevRound = lowerBracketRounds[lbRoundIndex - 1];

        if (currentRound && prevRound) {
            currentRound.matches.forEach((match, matchIndex) => {
                // For even-numbered LB rounds (LBR2, LBR4, etc.), all participants come from previous LB round
                if (lbRoundIndex % 2 === 1) {
                    // Even-numbered rounds only advance LB winners
                    const sourceMatch1Index = matchIndex * 2;
                    const sourceMatch2Index = matchIndex * 2 + 1;

                    if (sourceMatch1Index < prevRound.matches.length) {
                        const sourceMatch1 = prevRound.matches[sourceMatch1Index];
                        if (match.participant1Id === null) {
                            match.participant1Id = `TBD_${sourceMatch1.id}`;
                        }
                    }

                    if (sourceMatch2Index < prevRound.matches.length) {
                        const sourceMatch2 = prevRound.matches[sourceMatch2Index];
                        if (match.participant2Id === null) {
                            match.participant2Id = `TBD_${sourceMatch2.id}`;
                        }
                    }
                } else {
                    // Odd-numbered rounds (LBR3, LBR5, etc.) get one participant from previous LB round
                    if (matchIndex < prevRound.matches.length) {
                        const sourceMatch = prevRound.matches[matchIndex];
                        if (match.participant1Id === null) {
                            match.participant1Id = `TBD_${sourceMatch.id}`;
                        }
                    }
                }
            });
        }
    }

    // Link Losers Final to Grand Final
    if (lowerBracketRounds.length > 0) {
        const losersFinal = lowerBracketRounds[lowerBracketRounds.length - 1];
        if (losersFinal.matches.length > 0) {
            const grandFinalMatch = Object.values(matchesById).find(match =>
                match.identifier === "Grand Final"
            );
            if (grandFinalMatch) {
                losersFinal.matches[0].nextMatchId = grandFinalMatch.id;
            }
        }
    }

    // --- Grand Finals ---
    let grandFinalsRound = [];
    const ubFinalMatch = upperBracketRounds[upperBracketRounds.length - 1]?.matches[0];
    const lbFinalMatch = lowerBracketRounds[lowerBracketRounds.length - 1]?.matches[0];

    if (ubFinalMatch && lbFinalMatch) {
        const gfMatchId = `GFM${matchIdCounter++}`;
        const gfMatch = {
            id: gfMatchId,
            roundIndex: upperBracketRounds.length + lowerBracketRounds.length,
            matchIndexInRound: 0,
            participant1Id: null, // Winner from upper bracket final
            participant2Id: null, // Winner from lower bracket final
            winnerId: null,
            loserId: null,
            status: 'PENDING',
            score: null,
            nextMatchId: null,
            nextLoserMatchId: null,
            isUpperBracket: false,
            identifier: "Grand Final",
        };

        // Link finals to grand final
        ubFinalMatch.nextMatchId = gfMatchId;
        lbFinalMatch.nextMatchId = gfMatchId;

        grandFinalsRound.push({ id: 'GFR1', name: "Grand Final", matches: [gfMatch] });
        matchesById[gfMatchId] = gfMatch;

        // Grand Final Reset (if lower bracket winner wins first grand final)
        const resetMatchId = `GFM${matchIdCounter++}`;
        const resetMatch = {
            id: resetMatchId,
            roundIndex: upperBracketRounds.length + lowerBracketRounds.length + 1,
            matchIndexInRound: 0,
            participant1Id: null, // Same participants as first grand final
            participant2Id: null,
            winnerId: null,
            loserId: null,
            status: 'PENDING',
            score: null,
            nextMatchId: null,
            nextLoserMatchId: null,
            isUpperBracket: false,
            identifier: "Grand Final Reset",
        };

        gfMatch.nextMatchId = resetMatchId;
        grandFinalsRound.push({ id: 'GFR2', name: "Grand Final Reset", matches: [resetMatch] });
        matchesById[resetMatchId] = resetMatch;
    }

    // Set proper identifiers for key matches
    if (upperBracketRounds.length > 0) {
        const ubFinalRound = upperBracketRounds[upperBracketRounds.length - 1];
        if (ubFinalRound.matches.length === 1) {
            ubFinalRound.name = "Winners Final";
            matchesById[ubFinalRound.matches[0].id].identifier = "Winners Final";
        }
        if (upperBracketRounds.length > 1) {
            const ubSemiFinalRound = upperBracketRounds[upperBracketRounds.length - 2];
            if (ubSemiFinalRound.matches.length <= 2) {
                ubSemiFinalRound.name = "Winners Semi-Finals";
                ubSemiFinalRound.matches.forEach((match, index) => {
                    matchesById[match.id].identifier = `Winners Semi-Final ${index + 1}`;
                });
            }
        }
    }

    if (lowerBracketRounds.length > 0) {
        const lbFinalRound = lowerBracketRounds[lowerBracketRounds.length - 1];
        if (lbFinalRound.matches.length === 1) {
            lbFinalRound.name = "Losers Final";
            matchesById[lbFinalRound.matches[0].id].identifier = "Losers Final";
        }
    }

    // Combine all rounds and matches
    roundsData.push(...upperBracketRounds);
    roundsData.push(...lowerBracketRounds);
    roundsData.push(...grandFinalsRound);

    return {
        format: 'double-elimination',
        participants: initialParticipants,
        rounds: roundsData,
        matchesById,
        metadata: {
            numUpperRounds: upperBracketRounds.length,
            numLowerRounds: lowerBracketRounds.length,
            hasResetMatch: grandFinalsRound.length > 1,
            totalMatches: Object.keys(matchesById).length
        }
    };
}




// --- Round Robin Schedule Generator ---

/**
 * Generates a round robin schedule (each participant plays every other participant once).
 * @param {Participant[]} initialParticipants - Array of participant objects.
 * @returns {BracketData} The generated schedule data (format is 'round-robin', rounds contain all matches).
 */
function generateRoundRobinSchedule(initialParticipants) {
    if (!initialParticipants || initialParticipants.length < 2) {
        throw new Error("Round robin schedule requires at least 2 participants.");
    }

    const participants = prepareParticipants(initialParticipants); // Shuffle or seed
    const numParticipants = participants.length;
    const matches = [];
    const matchesById = {};
    let matchIdCounter = 1;

    // Algorithm to generate pairs:
    // For each participant, pair them with every subsequent participant in the list.
    for (let i = 0; i < numParticipants; i++) {
        for (let j = i + 1; j < numParticipants; j++) {
            const matchId = `RR_M${matchIdCounter++}`;
            const match = {
                id: matchId,
                roundIndex: 0, // All matches can be considered part of one "round" or phase
                matchIndexInRound: matches.length,
                participant1Id: participants[i].id,
                participant2Id: participants[j].id,
                winnerId: null,
                loserId: null,
                status: 'PENDING',
                score: null,
            };
            matches.push(match);
            matchesById[match.id] = match;
        }
    }

    // Optionally, structure into "rounds" if using the circle method for scheduling
    // For this basic version, all matches are in one conceptual round.
    const roundsData = [{
        id: "RR_R1",
        name: "Round Robin Matches",
        matches: matches
    }];

    return {
        format: 'round-robin',
        participants: initialParticipants,
        rounds: roundsData,
        matchesById,
        metadata: { totalMatches: matches.length }
    };
}


// --- Main Generator Function ---

/**
 * Generates a tournament bracket or schedule based on the specified format.
 * @param {Participant[]} participants - Array of participants or teams.
 * For team formats, each Participant object should represent a team,
 * e.g., { id: 'team1', name: 'Team Alpha', members: ['playerA', 'playerB'] }.
 * @param {string} format - Tournament format ('single-elim', 'double-elim', 'round-robin').
 * @param {Object} [options={}] - Additional options (e.g., { customSeeding: true, matchFormat: 'bo3' }).
 * @returns {BracketData} The generated bracket data.
 * @throws {Error} if the format is unsupported or participant numbers are insufficient.
 */
function generateBracket(participants, format, options = {}) {
    if (!participants || !Array.isArray(participants)) {
        throw new Error("Participants must be an array.");
    }

    // Note: Team formation is now handled in generateBracketForTournament.js
    // The bracket generator works with any participant objects (individual or team)

    // Normalize format string
    format = format.toLowerCase().replace(/-/g, '');

    // Generate bracket based on format
    let bracketData;
    switch (format) {
        case 'singleelim':
        case 'single-elim':
        case 'singleelimination':
        case 'single-elimination':
            bracketData = generateSingleEliminationBracket(participants);
            break;

        case 'doubleelim':
        case 'double-elim':
        case 'doubleelimination':
        case 'double-elimination':
            bracketData = generateDoubleEliminationBracket(participants);
            break;

        case 'roundrobin':
        case 'round-robin':
            bracketData = generateRoundRobinSchedule(participants);
            break;

        default:
            throw new Error(`Unsupported tournament format: ${format}`);
    }

    // Add match format information to all matches if specified
    if (options.matchFormat) {
        const matchFormat = options.matchFormat.toLowerCase();
        const validFormats = ['bo1', 'bo3', 'bo5', 'bo7'];

        if (validFormats.includes(matchFormat)) {
            Object.values(bracketData.matchesById).forEach(match => {
                match.matchFormat = matchFormat;
                match.maxGames = parseInt(matchFormat.substring(2)); // Extract number from 'bo3' -> 3
                match.firstTo = Math.ceil(match.maxGames / 2); // First to win majority
            });

            // Add to metadata
            bracketData.metadata = bracketData.metadata || {};
            bracketData.metadata.matchFormat = matchFormat;
        }
    }

    return bracketData;
}

// Export the main generator
module.exports = {
    generateBracket
};