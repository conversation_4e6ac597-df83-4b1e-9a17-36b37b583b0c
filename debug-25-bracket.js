// Debug 25-Player Bracket Structure
const { generateBracket } = require('./functions/bracketGenerator');

// Generate 25 participants
const participants = [];
for (let i = 1; i <= 25; i++) {
    participants.push({
        id: `0x${i.toString().padStart(40, '0')}`,
        name: `Player ${i.toString().padStart(2, '0')}`,
        walletAddress: `0x${i.toString().padStart(40, '0')}`,
        seed: i
    });
}

console.log('=== 25-PLAYER BRACKET STRUCTURE DEBUG ===\n');

const bracket = generateBracket(participants, 'double-elimination', { matchFormat: 'bo1' });

console.log(`Total participants: ${participants.length}`);
console.log(`Total matches: ${Object.keys(bracket.matchesById).length}`);
console.log(`Upper bracket rounds: ${bracket.metadata.numUpperRounds}`);
console.log(`Lower bracket rounds: ${bracket.metadata.numLowerRounds}`);

function getParticipantName(participantId) {
    if (!participantId) return 'null';
    if (participantId === 'BYE') return 'BYE';
    if (participantId.startsWith('TBD_')) return participantId;
    
    const participant = participants.find(p => p.id === participantId);
    return participant ? participant.name : participantId.substring(0, 8) + '...';
}

// Display bracket structure
console.log('\n=== BRACKET STRUCTURE ===');

bracket.rounds.forEach((round, roundIndex) => {
    console.log(`\n${round.name}:`);
    round.matches.forEach(match => {
        const p1Name = getParticipantName(match.participant1Id);
        const p2Name = getParticipantName(match.participant2Id);
        
        const bracketType = match.isUpperBracket ? 'UB' : 'LB';
        const nextMatch = match.nextMatchId ? ` -> ${match.nextMatchId}` : '';
        const nextLoserMatch = match.nextLoserMatchId ? ` (L-> ${match.nextLoserMatchId})` : '';
        
        console.log(`  [${bracketType}] ${match.id}: ${p1Name} vs ${p2Name} [${match.status}]${nextMatch}${nextLoserMatch}`);
    });
});

// Analyze potential issues
console.log('\n=== ANALYSIS ===');

// Check for null participants that should be byes
const nullParticipantMatches = Object.values(bracket.matchesById).filter(match => 
    match.participant1Id === null || match.participant2Id === null
);

if (nullParticipantMatches.length > 0) {
    console.log(`\n❌ Found ${nullParticipantMatches.length} matches with null participants:`);
    nullParticipantMatches.forEach(match => {
        console.log(`  ${match.id}: ${getParticipantName(match.participant1Id)} vs ${getParticipantName(match.participant2Id)}`);
    });
}

// Check for TBD placeholders that might be problematic
const tbdMatches = Object.values(bracket.matchesById).filter(match => 
    (match.participant1Id && match.participant1Id.startsWith('TBD_')) ||
    (match.participant2Id && match.participant2Id.startsWith('TBD_'))
);

console.log(`\nTBD placeholder matches: ${tbdMatches.length}`);

// Check upper bracket structure
console.log('\n=== UPPER BRACKET ANALYSIS ===');
const upperRounds = bracket.rounds.filter(round => 
    round.matches.some(match => match.isUpperBracket)
);

upperRounds.forEach((round, index) => {
    const matchCount = round.matches.length;
    const expectedParticipants = matchCount * 2;
    console.log(`${round.name}: ${matchCount} matches (${expectedParticipants} participants expected)`);
});

// Check lower bracket structure
console.log('\n=== LOWER BRACKET ANALYSIS ===');
const lowerRounds = bracket.rounds.filter(round => 
    round.matches.some(match => !match.isUpperBracket && !match.identifier?.includes('Grand Final'))
);

lowerRounds.forEach((round, index) => {
    const matchCount = round.matches.length;
    console.log(`${round.name}: ${matchCount} matches`);
});

console.log('\n=== DEBUG COMPLETE ===');
